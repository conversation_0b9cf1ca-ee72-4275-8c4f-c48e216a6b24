# Terraform-Windows-VM

This project is focused on the creation of a Virtual Machine for a Vsphere provider.

Be sure to read up on both the terraform guides and the
[wiki pages](https://gitlab.ea.com/dre-cobra/silverback/terraform/terraform-windows-vm/wikis/home)
on this GitLab repository.

You can also check out the auto-generated markdown representations of the terraform files on the
[Gitlab page for the project](https://dre-cobra.gitlab.ea.com/silverback/terraform/terraform-windows-vm/).

[[_TOC_]]

## Getting Started

If you have never used Terraform, It's recommened you watch these videos and
please [read](https://www.terraform.io/intro/index.html) up on how terraform works.

* [vBrownBag](https://www.youtube.com/watch?v=nQ7oRSi6mBU) - Using Terraform with vSphere, <PERSON>
* [vBrownBag](https://www.youtube.com/watch?v=ag36WCKrq5g) - VMware Infrastructure as Code with <PERSON>

Another usefull more in depth guide on setting everything up for a Terraform\Vsphere environment.

* [Inkubate](https://blog.inkubate.io/deploy-a-vmware-vsphere-virtual-machine-with-terraform/) - Deploy a VSphere VM with Terraform by <PERSON> <PERSON>ennet

### Prerequisites & Setup

Terraform is very simple to get working locally.
Follow the instructions below to setup a local environment, all files needed
should be in this git repository.

* [Download](https://www.terraform.io/intro/index.html) the appropriate Terraform

Package for your OS. Most likely windows 64bit.
I recommned putting this binary into an area you can remember and is safe.

Once the binary is downladed and extracted be sure to set the path to terraform
into your windows environment path.

1. Go to Control Panel -> System -> Advanced System Settings -> Environment Variables
2. Scroll down in system variables until you find PATH
3. Click edit and add terraform binary location,
   (If not Windows 10 be sure to add ; between paths, e.g. c:\path1;c:\path2)
4. Launch a new Console to take affect, if this does not work a restart
   may be needed or you have not set the environmental varaible correctly.

Once Terraform is setup be sure to navigate to a location via your git command
prompt where you would like the project to be, once in a location;

Clone the repo to your chosen location
**git clone <*****************>:dice-build/terraform/terraform-windows-vm.git**

Navigate to the new repo
**cd terraform-windows-vm**

## Running the tests

Running tests is incredibly easy with terraform.
Follow the instructions below will initialize terraform and create a plan.

This will initialize terraform on your machine fully.
**terraform init -input=false** to initialize the working directory.

This will run what is currently set in the mainBuild.tf and globalSetting.tf files.
Unless you have set the passwords and variables in the globalSettings.tf file with
the correct values or even fake values to just test and validate it will just fail.
**terraform plan -out=tfplan -input=false** to create a plan and save it to the local file tfplan.

This will enact the plan, **DO NOT** run this unless you intend to have a vm be created on VSphere
**terraform apply -input=false tfplan** to apply the plan stored in the file tfplan.

## Notes on Terraform

1. Environment variables can only populate string-type variables. List and map
   type variables must be populated via one of the other mechanisms.

2. Terraform will read environment variables in the form of TF_VAR_name to
   find the value for a variable. For example, the TF_VAR_access_key variable can be set to set the access_key variable.

3. ~~[Artifactory backend](https://github.com/hashicorp/terraform/issues/14795): URL cannot end with a trailing slash (/)~~

4. Changing CPU, RAM or any VM specific virtual hardware changes will shutdown the VM,
   perform the changes to all affected nodes then start the VM back up.
   Can take between 2 to 5 minutes depending on how quickly the VM shutdown.
   e.g. [Example Pipeline](https://gitlab.ea.com/dre-cobra/terraform/terraform-windows-vm/-/jobs/113289)

5. Renaming files will cause terraform to think that these are new resources and will therefore
   rebuild any nodes that are there and as such fail causing the statefile to error out.

6. Standard VM admin roles needs to start at the root level of the datacentre to allow terraform to work.

## Notes on Pipeline & Ansible

By default when the Ansible step runs on a VM it will be tagged with the commit of the Ansible repo.
This means that, by default, the Ansible step will only run on a VM if 1)
the Ansible repo was updated since last run, or 2) the VM is newly created and has thus not yet been tagged.

However there are cases when we want to run Ansible on a VM that is not new,
or even when the Ansible repo has not been updated.
For example, if a VM enters a bad state we might attempt to salvage it using Ansible.
Or alternatively if we have an update we want to roll out,
e.g. if an update was made to elipy-setup (which is installed by Ansible),
we might want to force Ansible to run.

To force Ansible to run on all VMs (in the project specified) simply set `FORCE_RUN_ANSIBLE_ALL` to `yes`.
Alternatively, pass along a list of comma separated VM IDs as a value for `FORCE_RUN_ANSIBLE_VMs`,
e.g. `xy2-abcde,xy2-bcdef, xy2-edcab`. (Note that it strips whitespaces between commas.)

## Fetch-Configs

The project configuration files, which were previously stored as a .json file within each project under project//,
have been moved to the [silverback-configs repo](https://gitlab.ea.com/dre-cobra/silverback/silverback-configs).
The files are downloaded for each file.
We need to keep using "needs" in the later stages, e.g attach, sync, ansible,
because the artifacts it stores ($project.json) will be downloaded.
Alternatively, we can artifacts these files in each stage for passing to the next one.

## Autogenerated Documentation

`terraform-docs` is a tool used to generate human-friendly summaries,
or orderly formatted documentation in tabular form, saved as .md files.
Pertinent information such as module or project .tf variables,
version requirements and so on are listed by header.

As we are using terraform-docs which produces Markdown files,
it is natural to use a static page generator that uses Markdown as well. MKDocs fulfills that purpose.
By storing the generated Markdown files under docs/, it is able to find and reference them in the page.

## Unlock Terraform statefile

Usually statefiles are locked because of previously aborted Pipeline Jobs or Jobs are currently running.
When the Job is aborted Terraform does not cleanly shutdown and statefiles can be left in a locked state.

### Resources

* <https://www.terraform.io/cli/commands/force-unlock>
* <https://docs.gitlab.com/ee/ci/yaml/#rules>

### Steps

The `unlock` Job can only be ran on the `master` branch and when you specify the `TARGET_PROJECT` and `LOCK_ID`.

1. Get the `ID` from the `Lock Info` section in the failed Job log
2. Go to <https://gitlab.ea.com/dre-cobra/silverback/terraform/terraform-windows-vm/-/pipelines/new> fill in:
   * `TARGET_PROJECT`: see description for example
   * `LOCK_ID`: from step (1)
3. Click `Run pipeline`
4. Check the log of the Job, if all go well you should see `Terraform state has been successfully unlocked!` and then you rerun the `terraform plan` Job.

### Common Issues

* `Failed to unlock state: failed to retrieve lock info: unexpected end of JSON input`
  * Check that you are running the job on the `master` branch.
* `Failed to unlock state: LocalState not locked`
  * Check that you have supplied the correct `TARGET_PROJECT`
  * This also indicates that you are not using the remote statefile

## TF upgrade and Statefile migration

After we migrate from artifactory to S3 as backend, we created subfolder for different TF version, e.g:
from v13 to v1.0.0.
The reason to create a new one (i.e v1.0.0, which should be better as v1 for current design):

* prevent non-backwards compatiable by TF
* can test on project basis, not all projects in one go
But this also requires certain manual work:
* pause on all pipelines in gitlab-ci
* manually copy the latest statefile from old folder (i.e v13) to new folder(i.e v1.0.0)
Due to this extra work, the easier way is to keep major version and skip change between minor and patch version.
For example:
when we upgrade TF from 1.0.0 to 1.0.11 in [base image](https://gitlab.ea.com/dre-cobra/container-images/python-terraform)
we can keep using terraform-state/v1.0.0/<project>/terraform.tfstate instead of change it to terraform-state/v1.0.11/<project>/terraform.tfstate
This allows us to create a new v2 subfolder only when we migrate from v1.M.N to v2.X.Y.
So the process of upgrade TF version:

1. From A.B.C to A.E.F

* update [TF version](https://gitlab.ea.com/dre-cobra/container-images/python-terraform/-/blob/8f79e420bc38b9c22b01b05793170e1c732aa33b/.gitlab-ci.yml#L26) to A.E.F
* make sure pipeline runs well and new image is created
* update *Build.tf file(s) in projects folder in this repo, to replace vA.B.C to vA (if not vA.B.C but only vA, no updates needed)
* commit changes
* good to notify team next pipeline runs on new TF version

2. From L.M.N to X.Y.Z

* update [TF version](https://gitlab.ea.com/dre-cobra/container-images/python-terraform/-/blob/8f79e420bc38b9c22b01b05793170e1c732aa33b/.gitlab-ci.yml#L26) to X.Y.Z
* make sure pipeline runs well and new image is created
* update *Build.tf file(s) in projects folder in this repo, to replace vL(or v.L.M.N )to vX
* notify team and stop pipeline(s) in gitlab-ci if it is running on scheduler
* commit changes
* go to S3, find bucket 'dreeu-generic-local' to create a subfolder 'vX' and copy the content from 'vL' (or vL.M.N if this exists) to 'vX'

3. start pipeline(s) and see if everything works well in "plan" stage:

* [ ] "$ terraform --version" prints out correct new version
* [ ] "No changes. Your infrastructure matches the configuration" should be there without any updates in the resource

## Troubleshooting

### Error: could not get category for name

```log
│ Error: could not get category for name "cobra-vm-status": list categories: GET https://vc.dice.ad.ea.com/rest/com/vmware/cis/tagging/category: 500 Internal Server Error
│
│   with module.dynamic_local_module_primary2["troy_play_stage_xb1_2"].data.vsphere_tag_category.cobra-vm-status,
│   on ../../modules/windows_attach_module_v3.3/main.tf line 52, in data "vsphere_tag_category" "cobra-vm-status":
│   52: data "vsphere_tag_category" "cobra-vm-status" {
│
╵
```

This appears to just be an issue with vSphere - give it some time and try again

### FullyQualifiedErrorId : Failed to sync all VMs, please review logs for details

```log
Failed to sync all VMs, please review logs for details: kr2-03a99d kr2-d10246 kr2-8132d7 kr2-3adfa9 kr2-93ade4
kr2-915e4e kr2-974e4d kr2-03a99d kr2-d10246 kr2-8132d7 kr2-3adfa9 kr2-93ade4 kr2-915e4e kr2-974e4d
At C:\GitLab-Runner\builds\Rv3GCQSr\0\dre-cobra\silverback\terraform\terraform-windows-vm\attache\run_sync.ps1:219
char:9
+         throw "Failed to sync all VMs, please review logs for details ...
+         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : OperationStopped: (Failed to sync ...5e4e kr2-974e4d:String) [], RuntimeException
    + FullyQualifiedErrorId : Failed to sync all VMs, please review logs for details: kr2-03a99d kr2-d10246 kr2-8132d7
    kr2-3adfa9 kr2-93ade4 kr2-915e4e kr2-974e4d kr2-03a99d kr2-d10246 kr2-8132d7 kr2-3adfa9 kr2-93ade4 kr2-915e4e kr2
  -974e4d
```

Sometimes it can take a really long time before a new VM is able to accept a new connection.
We do already have a retry & backoff strategy in place but sometimes this isn't enough. Just try again.

## Contributing

Please read [CONTRIBUTING.md](https://gitlab.ea.com/dre-cobra/terraform/terraform-windows-vm/blob/master/CONTRIBUTING.md)
for details on code structure, and the process for submitting requests.
