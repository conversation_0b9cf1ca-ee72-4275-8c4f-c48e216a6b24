# Mandatory
default:
  name: "nfsupgrade"
  script_path:
    - "TnT\\Bin\\Python\\virtual\\Lib\\site-packages\\dice_elipy_scripts"
    - "Python\\virtual\\Lib\\site-packages\\dice_elipy_scripts"
  project_name: "nfsupgrade"
  studio_location: "Guildford"
  avalanche_symbol_server: "dreuk-avsymbol.eu.ad.ea.com"
  symbol_stores_share: "\\\\eauk-file.eu.ad.ea.com\\NFSUpgrade\\Autobuilds"
  symbol_stores_suffix: "SymStore"
  vault_symstore: "true"
  vault_destination: "\\\\eauk-file.eu.ad.ea.com\\Vault\\\NFSUpgrade"
  vault_verification_config_path: "vault_verification_config_nfsupgrade.yml"
  md5_exf_path: "Build\\Jenkins\\Common\\Scripts\\Util\\exf.exe"
  ant_local_dir: "ANT_Source"
#  fetch_xb_basepackage: "true"

  bilbo_api_version: 2
  bilbo_url: "https://nfs-bilbo-eck.cobra.dre.ea.com"
  build_share: "\\\\eauk-file.eu.ad.ea.com\\NFSUpgrade\\Autobuilds"
  shift_tool_url: "https://artifacts.at.ea.com/artifactory/list/dreeu-generic-local/shiftsubmission/4.0.1"

  metrics_url: "https://dre-metrics-eck.cobra.dre.ea.com"
  metrics_port: 80

  jenkins_metrics_url: "https://criterion-metrics-eck.cobra.dre.ea.com/"
  jenkins_metrics_port: 80

  game_binaries: ['NFSUpgrade.exe']

  elsa_patch: "false"
  use_onefs_api: "false"
  skip_frosty_game_config_flags: "true"
  shift_config_file: "shift_config_nfsupgrade.yml"
  shift_retention: 15
  release_candidate_retention: 2 # 2 CLs
  shift_submission_path: "\\\\eauk-file.eu.ad.ea.com\\NFSUpgrade\\Autobuilds\\Shift\\auto_submissions"
  tasks_to_kill:
    - 'AvalancheCLI.exe'
    - 'FrostyIsoTool.exe'
    - 'msbuild.exe'
    - 'nant.exe'
    - 'devenv.exe'
    - 'Tool.Pipeline_Win64_release_Dll.exe'
    - 'mspdbsrv.exe'
    - 'vctip.exe'
    - 'snowcacheserver.exe'
    - 'cl.exe'
    - 'animationapp.exe'
    - 'prospero-clang.exe'
    - 'eapm.exe'

  secrets:
    # Get Roboto server connection keys for bundling into Frosty server builds
    - where:
        build_type: frosty
        platform: server
      url: 'https://ess.ea.com'
      namespace: 'criterion-online-build-prod'
      role_id: '7dab5bce-33cf-d577-ae6f-16fe1d832cf8' # Role ID name is elipy-certrenderer
      secret_id_envvar: 'GAME_TEAM_SECRETS_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/NFS25/NFS25_GAMESERVER_SERVER.client.int.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_SERVER.client.int.eadp.ea.com.key'
        - path: '/NFS25/NFS25_GAMESERVER_SERVER.client.int.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_SERVER.client.int.eadp.ea.com.crt'
        - path: '/NFS25/NFS25_GAMESERVER_SERVER.client.prod.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_SERVER.client.prod.eadp.ea.com.key'
        - path: '/NFS25/NFS25_GAMESERVER_SERVER.client.prod.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_SERVER.client.prod.eadp.ea.com.crt'
        - path: '/NFS25/NFS25_GAMESERVER_QVT_SERVER.client.int.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_QVT_SERVER.client.int.eadp.ea.com.key'
        - path: '/NFS25/NFS25_GAMESERVER_QVT_SERVER.client.int.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_QVT_SERVER.client.int.eadp.ea.com.crt'
        - path: '/NFS25/NFS25_GAMESERVER_QVT_SERVER.client.prod.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_QVT_SERVER.client.prod.eadp.ea.com.key'
        - path: '/NFS25/NFS25_GAMESERVER_QVT_SERVER.client.prod.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_QVT_SERVER.client.prod.eadp.ea.com.crt'

    # Get Roboto server connection keys for bundling into Frosty linuxserver builds
    - where:
        build_type: frosty
        platform: linuxserver
      url: 'https://ess.ea.com'
      namespace: 'criterion-online-build-prod'
      role_id: '7dab5bce-33cf-d577-ae6f-16fe1d832cf8' # Role ID name is elipy-certrenderer
      secret_id_envvar: 'GAME_TEAM_SECRETS_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/NFS25/NFS25_GAMESERVER_SERVER.client.int.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_SERVER.client.int.eadp.ea.com.key'
        - path: '/NFS25/NFS25_GAMESERVER_SERVER.client.int.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_SERVER.client.int.eadp.ea.com.crt'
        - path: '/NFS25/NFS25_GAMESERVER_SERVER.client.prod.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_SERVER.client.prod.eadp.ea.com.key'
        - path: '/NFS25/NFS25_GAMESERVER_SERVER.client.prod.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_SERVER.client.prod.eadp.ea.com.crt'
        - path: '/NFS25/NFS25_GAMESERVER_QVT_SERVER.client.int.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_QVT_SERVER.client.int.eadp.ea.com.key'
        - path: '/NFS25/NFS25_GAMESERVER_QVT_SERVER.client.int.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_QVT_SERVER.client.int.eadp.ea.com.crt'
        - path: '/NFS25/NFS25_GAMESERVER_QVT_SERVER.client.prod.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_QVT_SERVER.client.prod.eadp.ea.com.key'
        - path: '/NFS25/NFS25_GAMESERVER_QVT_SERVER.client.prod.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_QVT_SERVER.client.prod.eadp.ea.com.crt'
    # Get Roboto Origin keys for bundling into Frosty PC builds
    - where:
        build_type: frosty
        platform: win64
      url: 'https://ess.ea.com'
      namespace: 'criterion-online-build-prod'
      role_id: '7dab5bce-33cf-d577-ae6f-16fe1d832cf8' # Role ID name is elipy-certrenderer
      secret_id_envvar: 'GAME_TEAM_SECRETS_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/NFS25/lic'
          key: 'lic-private-trial.key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Win32\Origin\GAME_OOA\lic-private-trial.key'
        - path: '/NFS25/lic'
          key: 'lic-private-dte.key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Win32\Origin\GAME_OOA\lic-private-dte.key'
        - path: '/NFS25/lic'
          key: 'lic-private.key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Win32\Origin\GAME_OOA\lic-private.key'
    - where:
        project_account: true
      url: 'https://ess.ea.com'
      namespace:  'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/merlin/accounts/main_account'
    - where:
        project_secrets: true
      url: 'https://ess.ea.com'
      namespace:  'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/merlin/secrets'

  # The numbers here for Code are 1 per CL.
  # The numbers for frosty/symbols are 1 per CL.
  path_retention:
#    - \\eauk-file.eu.ad.ea.com\NFSUpgrade\Autobuilds\offsite_basic\build-main: 40
    - \\eauk-file.eu.ad.ea.com\NFSUpgrade\Autobuilds\crashdumps\pipeline_crashdumps: 50
    - \\eauk-file.eu.ad.ea.com\NFSUpgrade\Autobuilds\Shift\auto_submissions: 30
#    - \\eauk-file.eu.ad.ea.com\NFSUpgrade\Shift\Drone-ElectricSquare\build-main: 20

  retention_categories:
    code:
      - 'default':                  50
    code_nomaster:
      - 'default' :                 10
    tnt_local:
      - 'default' :                 0
    frosty\roboto:
      - 'default' :                 5
    frosty\merlin:
      - 'default' :                 8
    symbols:
      - 'default': 0
    avalanchestate:
      - 'default' :              0
    ant_cache:
      - 'default' :              0
    webexport:
      - 'default' :              10

  spin_retention:
    - 'default': 5

  smoke_retention:
    - 'default': 5

criterion:
  build_share: "\\\\eauk-file.eu.ad.ea.com\\NFSUpgrade\\Autobuilds"
  snowcache_host:
    win64game: 'sc2-b3e484.dice.ad.ea.com'
    win64trial: 'sc2-b3e484.dice.ad.ea.com'
    win64server: 'sc2-b3e484.dice.ad.ea.com'
    linux64server: 'sc2-b3e484.dice.ad.ea.com'
    ps5: 'sc2-b3e484.dice.ad.ea.com'
    xbsx: 'sc2-b3e484.dice.ad.ea.com'
    tool: 'sc2-b3e484.dice.ad.ea.com'
