# Mandatory
default:
  script_path: # You can put your local dev path here when developing scripts
    - "D:\\gitLab\\elipy-scripts\\dice_elipy_scripts"
    - "C:\\Gitlab\\elipy\\elipy-scripts\\dice_elipy_scripts"
  project_name: "dev"
  avalanche_symbol_server: "dice-cas-sym.dice.ad.ea.com"
  vault_destination: "D:\\vault\\test"
  ant_local_dir: "ANT_Source"
  studio_location: "TEST"
  perforce_maxwait: 60
  perforce_retries: 3

  metadata_manager:
    primary:
      name: "bilbo_v2"
      attributes_filename: "build.json"
    secondary:
      - name: "bilbo"
        attributes_filename: "bilbo_v1_build.json"

  recompression_cache:
    win64: "kraken-123eba.dice.ad.ea.com"
    server: "kraken-123eba.dice.ad.ea.com"
    xb1: "kraken-8a5a05.dice.ad.ea.com"
    xbsx: "kraken-af9d92.dice.ad.ea.com"
    ps4: "kraken-60a94a.dice.ad.ea.com"
    ps5: "kraken-1a2821.dice.ad.ea.com"
    linuxserver: "kraken-4379f0.dice.ad.ea.com"

  bilbo_api_version: 2
  bilbo_url: "https://bilbo-test.loki.dice.se"
  build_share: "\\\\filer.dice.ad.ea.com\\builds\\Casablanca"

  filer_api_url: "https://it-sweden-api.dice.se/api/v1/smb"

  metrics_url: "https://dre-metrics-eck.cobra.dre.ea.com"
  metrics_port: 80

  jenkins_metrics_url: "dre-metrics-eck.cobra.dre.ea.com"
  jenkins_metrics_port: 443

  md5_exf_path: "Build\\Jenkins\\Common\\Scripts\\Util\\exf.exe"

  denuvo:
    project:
      retail: 'Battlefield V - Retail - V5'
      trial: 'Battlefield V - Trial - V5'
    servers: '*************:8002,*************:8002,*************:8002'
    server_url: 'https://oetk2-prod.codefusion.technology'
  game_binaries: ['bfv.exe', 'bfv_trial.exe']
  ps4_disk_code_branch: bf-release-ps4
  ps4_disk_code_changelist: 4369642
  ps4_disk_data_branch: bf-release-ps4
  ps4_disk_data_changelist: 3837595

  elsa_patch: "false"
  skip_frosty_game_config_flags: "false"

  vault_verification_config_path: "vault_verification_config_kingston.yml"

  # Avalanche Settings
  avalanche:
    avalanche_size: 150 # Size of the Avalanche store in GB
    propagate_gets: true # true or false, GET's from upstream
    expiration_time_in_day: 3 # Expiration time in days 3 = never

    # never, monday, tuesday etc
    defrag_day: 'never' # What day a defrag should be done (off so we decide)
    full_defrag_day: 'never' # What day a full defrag should be done (off so we decide)

    maintenance_time_of_day : 9999 # Next maintenance time of day in minutes, set to 9999 so it never runs so only we trigger it.
    maintenance_window_in_minutes : 90 # Maintenance window in minutes.

  # The numbers here for Code are 1 per CL.
  # The numbers for frosty/symbols are 1 per CL.
  retention_categories:
      code:
        - 'default' :              5
        - 'game-dev' :             100
        - 'bf-stage' :             20
        - 'bf-release' :           40
        - 'bf-hotfix' :            15
      code_nomaster:
        - 'default' :              10
      tnt_local:
        - 'default' :              40
      frosty\casablanca:
        - 'default' :               20
        - 'dice-stage' :            60
        - 'game-dev' :              60
        - 'game-dev-unverified' :   60
        - 'game-dev-task3' :        25
        - 'feature-1' :             30
        - 'game-dev-dakar' :        25
        - 'game-dev-hack' :         25
        - 'game-dev-armory' :       25
        - 'bf-stage' :              60
        - 'bf-hotfix' :             60
        - 'bf-release' :            80
      symbols:
        - 'default' :               100
        - 'game-dev' :              350
        - 'bf-stage' :              250
        - 'bf-release' :            250
        - 'bf-hotfix' :             250
      publishedbuilds:
        - 'default' :                50
      stateless\code:
        - 'default' :                20
      stateless\tnt_local:
        - 'default' :                20
      stateless\symbols:
        - 'default' :                20
      stateless\frosty:
        - 'default' :                25
      stateless\avalanchestate:
        - 'default' :                50
      avalanchestate:
        - 'default' :                8
      ant_cache:
        - 'default' :                3
      webexport:
        - 'default' :                100

  shift_retention: 100
  release_candidate_retention: 56 # max_config(56) * number of release candidates
  shift_submission_path: "\\\\filer.dice.ad.ea.com\\builds\\Shift\\auto_submissions"
  tasks_to_kill:
    - 'AvalancheCLI.exe'
    - 'FrostyIsoTool.exe'
    - 'msbuild.exe'
    - 'snowcacheserver.exe'
    - 'eapm.exe'

  path_retention:
    - \\filer.dice.ad.ea.com\builds\DICE\rbt\crashdumps\pipeline_crashdumps: 100
    - \\eauk-file.eu.ad.ea.com\Roboto\Builds\Shift\ML\PC: 12

  required_vault_files:
    win64:
      - 'fil1'
      - 'fil2'
    ps4:
      - 'fake_req'

  secrets:
    # Get Casablanca server connection keys for bundling into Frosty server builds
    - where:
        build_type: frosty
        platform: server
      url: 'https://ess.ea.com'
      namespace:  'dice-online-casablanca-prod'
      role_id: '0feab1e9-ba7e-acaa-610e-4365569bceb6'
      secret_id_envvar: 'GAME_TEAM_SECRETS_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/bf19/game-server.client.int.eadp'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\BF19-GAME-SERVER.client.int.eadp.ea.com.key'
        - path: '/bf19/game-server.client.int.eadp'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\BF19-GAME-SERVER.client.int.eadp.ea.com.crt'
        - path: '/bf19/game-server.client.prod.eadp'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\BF19-GAME-SERVER.client.prod.eadp.ea.com.key'
        - path: '/bf19/game-server.client.prod.eadp'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\BF19-GAME-SERVER.client.prod.eadp.ea.com.crt'
    # Get Casablanca server connection keys for bundling into Frosty linuxserver builds
    - where:
        build_type: frosty
        platform: linuxserver
      url: 'https://ess.ea.com'
      namespace:  'dice-online-casablanca-prod'
      role_id: '0feab1e9-ba7e-acaa-610e-4365569bceb6'
      secret_id_envvar: 'VAULT_ONLINE_CAS_PROD_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/bf19/game-server.client.int.eadp'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\BF19-GAME-SERVER.client.int.eadp.ea.com.key'
        - path: '/bf19/game-server.client.int.eadp'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\BF19-GAME-SERVER.client.int.eadp.ea.com.crt'
        - path: '/bf19/game-server.client.prod.eadp'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\BF19-GAME-SERVER.client.prod.eadp.ea.com.key'
        - path: '/bf19/game-server.client.prod.eadp'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\BF19-GAME-SERVER.client.prod.eadp.ea.com.crt'

    - where:
        build_type: account
        platform: all
      url: 'https://ess.ea.com'
      namespace:  'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/cobra/automation/accounts/monkey.casablanca'
          key: 'key'
          to: 'D:\test\cred.json'

test:
  build_share: "D:\\testcopy"

local:
  build_share: "\\\\filer.dice.ad.ea.com\\builds\\DICE\\fb1"

dice:
  bilbo_url: "https://bilbo-kin.dre.dice.se"
  build_share: "\\\\filer.dice.ad.ea.com\\builds\\Kingston"

battlefieldgame:
  bilbo_url: "https://bilbo-fb1.thor.dice.se"
  build_share: "\\\\filer.dice.ad.ea.com\\builds\\DiceUpgradeNext\\fb1"
  licensee_code_folder_name: "Code\\DICE"
