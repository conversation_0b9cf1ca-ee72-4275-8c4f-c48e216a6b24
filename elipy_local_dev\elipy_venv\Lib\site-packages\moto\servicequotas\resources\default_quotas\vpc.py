VPC_DEFAULT_QUOTAS = [
    {
        "Adjustable": True,
        "GlobalQuota": False,
        "QuotaArn": "arn:aws:servicequotas:eu-west-1::vpc/L-7E9ECCDB",
        "QuotaCode": "L-7E9ECCDB",
        "QuotaName": "Active VPC peering connections per VPC",
        "ServiceCode": "vpc",
        "ServiceName": "Amazon Virtual Private Cloud (Amazon VPC)",
        "Unit": "None",
        "Value": 50.0,
    },
    {
        "Adjustable": False,
        "GlobalQuota": False,
        "QuotaArn": "arn:aws:servicequotas:eu-west-1::vpc/L-3248932A",
        "QuotaCode": "L-3248932A",
        "QuotaName": "Characters per VPC endpoint policy",
        "ServiceCode": "vpc",
        "ServiceName": "Amazon Virtual Private Cloud (Amazon VPC)",
        "Unit": "None",
        "Value": 20480.0,
    },
    {
        "Adjustable": True,
        "GlobalQuota": False,
        "QuotaArn": "arn:aws:servicequotas:eu-west-1::vpc/L-45FE3B85",
        "QuotaCode": "L-45FE3B85",
        "QuotaName": "Egress-only internet gateways per Region",
        "ServiceCode": "vpc",
        "ServiceName": "Amazon Virtual Private Cloud (Amazon VPC)",
        "Unit": "None",
        "Value": 5.0,
    },
    {
        "Adjustable": True,
        "GlobalQuota": False,
        "QuotaArn": "arn:aws:servicequotas:eu-west-1::vpc/L-1B52E74A",
        "QuotaCode": "L-1B52E74A",
        "QuotaName": "Gateway VPC endpoints per Region",
        "ServiceCode": "vpc",
        "ServiceName": "Amazon Virtual Private Cloud (Amazon VPC)",
        "Unit": "None",
        "Value": 20.0,
    },
    {
        "Adjustable": True,
        "GlobalQuota": False,
        "QuotaArn": "arn:aws:servicequotas:eu-west-1::vpc/L-83CA0A9D",
        "QuotaCode": "L-83CA0A9D",
        "QuotaName": "IPv4 CIDR blocks per VPC",
        "ServiceCode": "vpc",
        "ServiceName": "Amazon Virtual Private Cloud (Amazon VPC)",
        "Unit": "None",
        "Value": 5.0,
    },
    {
        "Adjustable": True,
        "GlobalQuota": False,
        "QuotaArn": "arn:aws:servicequotas:eu-west-1::vpc/L-085A6257",
        "QuotaCode": "L-085A6257",
        "QuotaName": "IPv6 CIDR blocks per VPC",
        "ServiceCode": "vpc",
        "ServiceName": "Amazon Virtual Private Cloud (Amazon VPC)",
        "Unit": "None",
        "Value": 5.0,
    },
    {
        "Adjustable": True,
        "GlobalQuota": False,
        "QuotaArn": "arn:aws:servicequotas:eu-west-1::vpc/L-0EA8095F",
        "QuotaCode": "L-0EA8095F",
        "QuotaName": "Inbound or outbound rules per security group",
        "ServiceCode": "vpc",
        "ServiceName": "Amazon Virtual Private Cloud (Amazon VPC)",
        "Unit": "None",
        "Value": 60.0,
    },
    {
        "Adjustable": True,
        "GlobalQuota": False,
        "QuotaArn": "arn:aws:servicequotas:eu-west-1::vpc/L-29B6F2EB",
        "QuotaCode": "L-29B6F2EB",
        "QuotaName": "Interface VPC endpoints per VPC",
        "ServiceCode": "vpc",
        "ServiceName": "Amazon Virtual Private Cloud (Amazon VPC)",
        "Unit": "None",
        "Value": 50.0,
    },
    {
        "Adjustable": True,
        "GlobalQuota": False,
        "QuotaArn": "arn:aws:servicequotas:eu-west-1::vpc/L-A4707A72",
        "QuotaCode": "L-A4707A72",
        "QuotaName": "Internet gateways per Region",
        "ServiceCode": "vpc",
        "ServiceName": "Amazon Virtual Private Cloud (Amazon VPC)",
        "Unit": "None",
        "Value": 5.0,
    },
    {
        "Adjustable": True,
        "GlobalQuota": False,
        "QuotaArn": "arn:aws:servicequotas:eu-west-1::vpc/L-FE5A380F",
        "QuotaCode": "L-FE5A380F",
        "QuotaName": "NAT gateways per Availability Zone",
        "ServiceCode": "vpc",
        "ServiceName": "Amazon Virtual Private Cloud (Amazon VPC)",
        "Unit": "None",
        "Value": 5.0,
    },
    {
        "Adjustable": True,
        "GlobalQuota": False,
        "QuotaArn": "arn:aws:servicequotas:eu-west-1::vpc/L-B4A6D682",
        "QuotaCode": "L-B4A6D682",
        "QuotaName": "Network ACLs per VPC",
        "ServiceCode": "vpc",
        "ServiceName": "Amazon Virtual Private Cloud (Amazon VPC)",
        "Unit": "None",
        "Value": 200.0,
    },
    {
        "Adjustable": True,
        "GlobalQuota": False,
        "QuotaArn": "arn:aws:servicequotas:eu-west-1::vpc/L-BB24F6E5",
        "QuotaCode": "L-BB24F6E5",
        "QuotaName": "Network Address Usage",
        "ServiceCode": "vpc",
        "ServiceName": "Amazon Virtual Private Cloud (Amazon VPC)",
        "Unit": "None",
        "Value": 64000.0,
    },
    {
        "Adjustable": True,
        "GlobalQuota": False,
        "QuotaArn": "arn:aws:servicequotas:eu-west-1::vpc/L-DF5E4CA3",
        "QuotaCode": "L-DF5E4CA3",
        "QuotaName": "Network interfaces per Region",
        "ServiceCode": "vpc",
        "ServiceName": "Amazon Virtual Private Cloud (Amazon VPC)",
        "Unit": "None",
        "Value": 5000.0,
    },
    {
        "Adjustable": True,
        "GlobalQuota": False,
        "QuotaArn": "arn:aws:servicequotas:eu-west-1::vpc/L-DC9F7029",
        "QuotaCode": "L-DC9F7029",
        "QuotaName": "Outstanding VPC peering connection requests",
        "ServiceCode": "vpc",
        "ServiceName": "Amazon Virtual Private Cloud (Amazon VPC)",
        "Unit": "None",
        "Value": 25.0,
    },
    {
        "Adjustable": True,
        "GlobalQuota": False,
        "QuotaArn": "arn:aws:servicequotas:eu-west-1::vpc/L-2C462E13",
        "QuotaCode": "L-2C462E13",
        "QuotaName": "Participant accounts per VPC",
        "ServiceCode": "vpc",
        "ServiceName": "Amazon Virtual Private Cloud (Amazon VPC)",
        "Unit": "None",
        "Value": 100.0,
    },
    {
        "Adjustable": True,
        "GlobalQuota": False,
        "QuotaArn": "arn:aws:servicequotas:eu-west-1::vpc/L-CD17FD4B",
        "QuotaCode": "L-CD17FD4B",
        "QuotaName": "Peered Network Address Usage",
        "ServiceCode": "vpc",
        "ServiceName": "Amazon Virtual Private Cloud (Amazon VPC)",
        "Unit": "None",
        "Value": 128000.0,
    },
    {
        "Adjustable": True,
        "GlobalQuota": False,
        "QuotaArn": "arn:aws:servicequotas:eu-west-1::vpc/L-589F43AA",
        "QuotaCode": "L-589F43AA",
        "QuotaName": "Route tables per VPC",
        "ServiceCode": "vpc",
        "ServiceName": "Amazon Virtual Private Cloud (Amazon VPC)",
        "Unit": "None",
        "Value": 200.0,
    },
    {
        "Adjustable": True,
        "GlobalQuota": False,
        "QuotaArn": "arn:aws:servicequotas:eu-west-1::vpc/L-93826ACB",
        "QuotaCode": "L-93826ACB",
        "QuotaName": "Routes per route table",
        "ServiceCode": "vpc",
        "ServiceName": "Amazon Virtual Private Cloud (Amazon VPC)",
        "Unit": "None",
        "Value": 50.0,
    },
    {
        "Adjustable": True,
        "GlobalQuota": False,
        "QuotaArn": "arn:aws:servicequotas:eu-west-1::vpc/L-2AEEBF1A",
        "QuotaCode": "L-2AEEBF1A",
        "QuotaName": "Rules per network ACL",
        "ServiceCode": "vpc",
        "ServiceName": "Amazon Virtual Private Cloud (Amazon VPC)",
        "Unit": "None",
        "Value": 20.0,
    },
    {
        "Adjustable": True,
        "GlobalQuota": False,
        "QuotaArn": "arn:aws:servicequotas:eu-west-1::vpc/L-2AFB9258",
        "QuotaCode": "L-2AFB9258",
        "QuotaName": "Security groups per network interface",
        "ServiceCode": "vpc",
        "ServiceName": "Amazon Virtual Private Cloud (Amazon VPC)",
        "Unit": "None",
        "Value": 5.0,
    },
    {
        "Adjustable": True,
        "GlobalQuota": False,
        "QuotaArn": "arn:aws:servicequotas:eu-west-1::vpc/L-407747CB",
        "QuotaCode": "L-407747CB",
        "QuotaName": "Subnets per VPC",
        "ServiceCode": "vpc",
        "ServiceName": "Amazon Virtual Private Cloud (Amazon VPC)",
        "Unit": "None",
        "Value": 200.0,
    },
    {
        "Adjustable": True,
        "GlobalQuota": False,
        "QuotaArn": "arn:aws:servicequotas:eu-west-1::vpc/L-44499CD2",
        "QuotaCode": "L-44499CD2",
        "QuotaName": "Subnets that can be shared with an account",
        "ServiceCode": "vpc",
        "ServiceName": "Amazon Virtual Private Cloud (Amazon VPC)",
        "Unit": "None",
        "Value": 100.0,
    },
    {
        "Adjustable": False,
        "GlobalQuota": False,
        "QuotaArn": "arn:aws:servicequotas:eu-west-1::vpc/L-8312C5BB",
        "QuotaCode": "L-8312C5BB",
        "QuotaName": "VPC peering connection request expiry hours",
        "ServiceCode": "vpc",
        "ServiceName": "Amazon Virtual Private Cloud (Amazon VPC)",
        "Unit": "None",
        "Value": 168.0,
    },
    {
        "Adjustable": True,
        "GlobalQuota": False,
        "QuotaArn": "arn:aws:servicequotas:eu-west-1::vpc/L-E79EC296",
        "QuotaCode": "L-E79EC296",
        "QuotaName": "VPC security groups per Region",
        "ServiceCode": "vpc",
        "ServiceName": "Amazon Virtual Private Cloud (Amazon VPC)",
        "Unit": "None",
        "Value": 2500.0,
    },
    {
        "Adjustable": True,
        "GlobalQuota": False,
        "QuotaArn": "arn:aws:servicequotas:eu-west-1::vpc/L-F678F1CE",
        "QuotaCode": "L-F678F1CE",
        "QuotaName": "VPCs per Region",
        "ServiceCode": "vpc",
        "ServiceName": "Amazon Virtual Private Cloud (Amazon VPC)",
        "Unit": "None",
        "Value": 5.0,
    },
]
