# *************************************************************
#  Sets up the initial needs to point to our vSphere server
# *************************************************************
# Point to our datacentre
# https://www.terraform.io/docs/providers/vsphere/d/datacenter.html
#
# Notes:
# Datastore Cluster: https://www.terraform.io/docs/providers/vsphere/r/datastore_cluster.html
# Individual Datastores: https://www.terraform.io/docs/providers/vsphere/d/datastore.html
# Resource Pools: https://www.terraform.io/docs/providers/vsphere/d/resource_pool.html
# Networking: https://www.terraform.io/docs/providers/vsphere/d/network.html
# Templates: https://www.terraform.io/docs/providers/vsphere/r/virtual_machine.html
# Folder Managment: https://www.terraform.io/docs/providers/vsphere/r/folder.html
# UUID Usage: https://www.terraform.io/docs/providers/random/r/id.html
# Count Usage: https://www.terraform.io/intro/examples/count.html

# *************************************************************
# Terraform state artifactory backup.
# https://www.terraform.io/docs/backends/types/artifactory.html
# *************************************************************
terraform {
  backend "http" {
  }
}
# *************************************************************
# BCT: bct_autotest_eala, check CONTRIBUTING.md before editing here.
# *************************************************************

locals {
  module_settings = {
    # BCT AUTOTEST NODES
    "ps_autotests_win64_002"       = { datastore = "esx_build_ssd_ds04_build_vms_05", vm_count = "4", labels = "ps_eala statebuild_eala poolbuild_eala win64", cpu_core = "5" }
    "ps_autotests_ps5_002"         = { datastore = "esx_build_ssd_ds04_build_vms_05", vm_count = "4", labels = "ps_eala statebuild_eala poolbuild_eala ps5", cpu_core = "5" }
    "ps_autotests_xbsx_001"        = { datastore = "esx_build_ssd_ds04_build_vms_02", vm_count = "4", labels = "ps_eala statebuild_eala poolbuild_eala xbsx", cpu_core = "5" }
    "ps_autotests_linuxserver_001" = { datastore = "esx_build_ssd_ds04_build_vms_02", vm_count = "2", labels = "ps_eala statebuild_eala poolbuild_eala linuxserver", cpu_core = "6" }
    "ps_autotests_server_001"      = { datastore = "esx_build_ssd_ds04_build_vms_02", vm_count = "2", labels = "ps_eala statebuild_eala poolbuild_eala server", cpu_core = "6" }
    "ps_autotests_tool_001"        = { datastore = "esx_build_ssd_ds04_build_vms_05", vm_count = "2", labels = "ps_eala statebuild_eala poolbuild_eala tool", cpu_core = "6" }

    # "test_ps_autotests_server" = { datastore = "esx_build_ssd_ds04_build_vms_02", vm_count = "1", labels = "testenv_nodes_troy_01 ps_eala statebuild_eala poolbuild_eala server", cpu_core = "6", role = "https://test1-jenkins.cobra.dre.ea.com" }

    "ps_cpu_test_12_001" = { datastore = "esx_build_ssd_ds04_build_vms_02", vm_count = "1", labels = "ps_eala 12_core eala", cores_per_socket = "12", cpu_core = "12", ram_count = 131072, role = "https://bct-dev-jenkins.cobra.dre.ea.com/" }
    "ps_cpu_test_18_001" = { datastore = "esx_build_ssd_ds04_build_vms_02", vm_count = "1", labels = "ps_eala 18_core eala", cores_per_socket = "9", cpu_core = "18", ram_count = 131072, role = "https://bct-dev-jenkins.cobra.dre.ea.com/" }
    "ps_cpu_test_24_001" = { datastore = "esx_build_ssd_ds04_build_vms_02", vm_count = "1", labels = "ps_eala 24_core eala", cores_per_socket = "12", cpu_core = "24", ram_count = 131072, role = "https://bct-dev-jenkins.cobra.dre.ea.com/" }
  }
}

module "dynamic_local_module_primary" {
  source   = "../../modules/windows_attach_module_v3.5"
  for_each = local.module_settings

  vsphere_datastore       = each.value.datastore
  vm_count                = each.value.vm_count
  vm_prefix               = try(each.value.vm_prefix, "bcla-")
  jenkins_slave_labels    = each.value.labels
  role                    = try(each.value.role, "https://bct-autotest-jenkins.cobra.dre.ea.com/")
  vsphere_compute_cluster = try(each.value.compute_cluster, "Ripple Effect Build Farm")
  ram_count               = try(each.value.ram_count, 65536)
  jenkins_websocket       = try(each.value.jenkins_websocket, "disabled")
  cpu_core                = try(each.value.cpu_core, "")
  cores_per_socket        = try(each.value.cores_per_socket, "")

  cloning_timeout       = var.cloning_timeout
  vsphere_template      = var.packer_template
  vsphere_network       = var.network
  vsphere_datacenter    = var.datacenter
  vsphere_folder        = "DICE/terraform-nodes/bct_autotest_eala"
  domain_admin          = var.domain_admin
  domain_admin_password = var.domain_password
  local_admin_user      = var.local_username
  local_admin_password  = var.local_password
  local_admin_group     = var.local_admin_group
  project_dir           = var.project_dir
  project_name          = var.project_name
  commit_sha            = var.commit_sha
  commit_user           = var.commit_user
  commit_url            = var.commit_url
  disk_size             = var.disk_size
  domain_name           = var.domain_name
  domain_ou             = "OU=Granite,OU=EALA-Build Servers,DC=la,DC=ad,DC=ea,DC=com"
  hardware_version      = var.hardware_version
}

# *************************************************************
#  Setting up the dynamic output needed for downstream pipelines
# *************************************************************
# Notes:
# For Expressions: https://www.terraform.io/docs/language/expressions/for.html
# Key Functions: https://www.terraform.io/docs/language/functions/keys.html
# flatten Function: https://www.terraform.io/docs/language/functions/flatten.html
# Output Values: https://www.terraform.io/docs/language/values/outputs.html
# Local Values: https://www.terraform.io/docs/language/values/locals.html
#
# The solution outputs the same way as previously in output.tf
# example:
# node_name_uuids = [
#  {
#   "id"   = "JF5D"
#   "name" = "245e43"
#   },
#
# *************************************************************
#  Dynamic Output, check CONTRIBUTING.md before editing here.
# *************************************************************
locals {
  nodes_output = flatten([
    for mod in keys(local.module_settings) : [
      for node in module.dynamic_local_module_primary[mod].nodes : [
        {
          name              = node.name
          id                = node.id
          custom_attributes = node.custom_attributes
        }
      ]
    ]
  ])
  node_name_uuids_output = flatten([
    for mod in keys(local.module_settings) : [
      for node in module.dynamic_local_module_primary[mod].node_name_uuids : [
        {
          name = node.hex
          id   = node.id
        }
      ]
    ]
  ])
}

output "nodes" {
  value = local.nodes_output
}

output "node_name_uuids" {
  value = local.node_name_uuids_output
}
