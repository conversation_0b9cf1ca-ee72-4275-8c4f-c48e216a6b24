# Mandatory
default:
  name: "merlin"
  script_path:
    - "TnT\\Bin\\Python\\virtual\\Lib\\site-packages\\dice_elipy_scripts"
    - "Python\\virtual\\Lib\\site-packages\\dice_elipy_scripts"
  project_name: "merlin"
  studio_location: "Guildford"
  avalanche_symbol_server: "dreuk-avsymbol.eu.ad.ea.com"
  symbol_stores_share: "\\\\eauk-file.eu.ad.ea.com\\Merlin\\Autobuilds"
  symbol_stores_suffix: "SymStore"
  vault_symstore: "true"
  vault_destination: "\\\\eauk-file.eu.ad.ea.com\\Vault\\Merlin"
  vault_verification_config_path: "vault_verification_config_merlin.yml"
  md5_exf_path: "Build\\Jenkins\\Common\\Scripts\\Util\\exf.exe"
  ant_local_dir: "ANT_Source"
  spin_s3_bucket: "ups-nfs-merlin"
#  fetch_xb_basepackage: "true"

  bilbo_api_version: 2
  bilbo_url: "https://mer-bilbo-eck.cobra.dre.ea.com"
  build_share: "\\\\eauk-file.eu.ad.ea.com\\Merlin\\Autobuilds"
  metadata_manager:
    primary:
      name: "bilbo_v2"
      attributes_filename: "build.json"
    secondary:
      - name: "bilbo"
        index: "bilbo_v1"
        attributes_filename: "bilbo_v1.json"
  shift_tool_url: "https://artifacts.at.ea.com/artifactory/list/dreeu-generic-local/shiftsubmission/5.1.0"

  metrics_url: "https://dre-metrics-eck.cobra.dre.ea.com"
  metrics_port: 80

  jenkins_metrics_url: "https://criterion-metrics-eck.cobra.dre.ea.com/"
  jenkins_metrics_port: 80

  denuvo:
    artifactory_path: "https://artifacts.at.ea.com/artifactory/dreeu-generic-local/denuvo/merlin/denuvo_files_3.zip"
    project:
      retail: 'Need for Speed 2024 - Origin - Retail'
      trial: 'Need for Speed 2024 - Origin - Trial'
    servers: ''
    server_url: 'https://oetk2-prod.codefusion.technology'

  game_binaries: ['MerlinTrial.exe', 'Merlin.exe']

  # ps4_disk_code_branch: ShippingPS4
  # ps4_disk_code_changelist: 970080
  # ps4_disk_data_branch: ShippingPS4
  # ps4_disk_data_changelist: 970080

  elsa_patch: "false"
  use_onefs_api: "false"
  skip_frosty_game_config_flags: "true"
  shift_config_file: "shift_config_merlin.yml"
  shift_retention: 100
  release_candidate_retention: 56 # max_config(56) * number of release candidates
  shift_submission_path: "\\\\eauk-file.eu.ad.ea.com\\Merlin\\Autobuilds\\Shift\\auto_submissions"
  tasks_to_kill:
    - 'AvalancheCLI.exe'
    - 'FrostyIsoTool.exe'
    - 'msbuild.exe'
    - 'nant.exe'
    - 'devenv.exe'
    - 'Tool.Pipeline_Win64_release_Dll.exe'
    - 'mspdbsrv.exe'
    - 'vctip.exe'
    - 'snowcacheserver.exe'
    - 'cl.exe'
    - 'animationapp.exe'
    - 'prospero-clang.exe'
    - 'eapm.exe'

  secrets:
    # Get Roboto server connection keys for bundling into Frosty server builds
    - where:
        build_type: frosty
        platform: server
      url: 'https://ess.ea.com'
      namespace: 'criterion-online-build-prod'
      role_id: '7dab5bce-33cf-d577-ae6f-16fe1d832cf8' # Role ID name is elipy-certrenderer
      secret_id_envvar: 'GAME_TEAM_SECRETS_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/NFS25/NFS22_GAMESERVER_SERVER.client.int.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_SERVER.client.int.eadp.ea.com.key'
        - path: '/NFS25/NFS22_GAMESERVER_SERVER.client.int.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_SERVER.client.int.eadp.ea.com.crt'
        - path: '/NFS25/NFS25_GAMESERVER_SERVER.client.prod.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_SERVER.client.prod.eadp.ea.com.key'
        - path: '/NFS25/NFS25_GAMESERVER_SERVER.client.prod.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_SERVER.client.prod.eadp.ea.com.crt'
        - path: '/NFS25/NFS25_GAMESERVER_QVT_SERVER.client.int.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_QVT_SERVER.client.int.eadp.ea.com.key'
        - path: '/NFS25/NFS25_GAMESERVER_QVT_SERVER.client.int.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_QVT_SERVER.client.int.eadp.ea.com.crt'
        - path: '/NFS25/NFS25_GAMESERVER_QVT_SERVER.client.prod.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_QVT_SERVER.client.prod.eadp.ea.com.key'
        - path: '/NFS25/NFS25_GAMESERVER_QVT_SERVER.client.prod.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_QVT_SERVER.client.prod.eadp.ea.com.crt'

    # Get Roboto server connection keys for bundling into Frosty linuxserver builds
    - where:
        build_type: frosty
        platform: linuxserver
      url: 'https://ess.ea.com'
      namespace: 'criterion-online-build-prod'
      role_id: '7dab5bce-33cf-d577-ae6f-16fe1d832cf8' # Role ID name is elipy-certrenderer
      secret_id_envvar: 'GAME_TEAM_SECRETS_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/NFS25/NFS22_GAMESERVER_SERVER.client.int.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_SERVER.client.int.eadp.ea.com.key'
        - path: '/NFS25/NFS22_GAMESERVER_SERVER.client.int.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_SERVER.client.int.eadp.ea.com.crt'
        - path: '/NFS25/NFS25_GAMESERVER_SERVER.client.prod.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_SERVER.client.prod.eadp.ea.com.key'
        - path: '/NFS25/NFS25_GAMESERVER_SERVER.client.prod.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_SERVER.client.prod.eadp.ea.com.crt'
        - path: '/NFS25/NFS25_GAMESERVER_QVT_SERVER.client.int.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_QVT_SERVER.client.int.eadp.ea.com.key'
        - path: '/NFS25/NFS25_GAMESERVER_QVT_SERVER.client.int.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_QVT_SERVER.client.int.eadp.ea.com.crt'
        - path: '/NFS25/NFS25_GAMESERVER_QVT_SERVER.client.prod.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_QVT_SERVER.client.prod.eadp.ea.com.key'
        - path: '/NFS25/NFS25_GAMESERVER_QVT_SERVER.client.prod.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_QVT_SERVER.client.prod.eadp.ea.com.crt'
    # Get Roboto Origin keys for bundling into Frosty PC builds
    - where:
        build_type: frosty
        platform: win64
      url: 'https://ess.ea.com'
      namespace: 'criterion-online-build-prod'
      role_id: '7dab5bce-33cf-d577-ae6f-16fe1d832cf8' # Role ID name is elipy-certrenderer
      secret_id_envvar: 'GAME_TEAM_SECRETS_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/NFS25/lic'
          key: 'lic-private-trial.key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Win32\Origin\GAME_OOA\lic-private-trial.key'
        - path: '/NFS25/lic'
          key: 'lic-private-dte.key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Win32\Origin\GAME_OOA\lic-private-dte.key'
        - path: '/NFS25/lic'
          key: 'lic-private.key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Win32\Origin\GAME_OOA\lic-private.key'
    - where:
        build_type: drone
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID' # Matches a Jenkins Credential
      values:
        - '/cobra/automation/shift/automation_credentials'
    - where:
        project_account: true
      url: 'https://ess.ea.com'
      namespace:  'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/merlin/accounts/main_account'
    - where:
        project_secrets: true
      url: 'https://ess.ea.com'
      namespace:  'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/merlin/secrets'

  # The numbers here for Code are 1 per CL.
  # The numbers for frosty/symbols are 1 per CL.
  path_retention:
    - \\eauk-file.eu.ad.ea.com\Merlin\Autobuilds\offsite_basic\build-main: 40
    - \\eauk-file.eu.ad.ea.com\Merlin\Autobuilds\crashdumps\pipeline_crashdumps: 50
    - \\eauk-file.eu.ad.ea.com\Merlin\Autobuilds\offsite_basic_drone\build-main: 20
    - \\eauk-file.eu.ad.ea.com\Merlin\Autobuilds\offsite_basic_drone\build-stage: 20
    - \\eauk-file.eu.ad.ea.com\Merlin\Autobuilds\Shift\auto_submissions: 15
    - \\eauk-file.eu.ad.ea.com\Merlin\Shift\auto_submissions: 0

  retention_categories:
    code:
      - 'default':                  50
      - 'build-main-dre':           5
      - 'build-release' :           100
      - 'build-hotfix' :            25
    code_nomaster:
      - 'default' :                 10
    tnt_local:
      - 'default' :                 0
    frosty\roboto:
      - 'default' :                 5
    frosty\merlin:
      - 'default' :                 30
      - 'build-main-dre':           10
      - 'build-hotfix' :            20
    symbols:
      - 'default': 0
    avalanchestate:
      - 'default' :              0
    ant_cache:
      - 'default' :              0
    webexport:
      - 'default' :              0

  spin_retention:
    - 'default': 5

criterion:
  build_share: "\\\\eauk-file.eu.ad.ea.com\\Merlin\\Autobuilds"
  snowcache_host:
    win64game: 'sc2-950bc5.dice.ad.ea.com'
    win64trial: 'sc2-950bc5.dice.ad.ea.com'
    win64server: 'sc2-950bc5.dice.ad.ea.com'
    linux64server: 'sc2-950bc5.dice.ad.ea.com'
    ps5: 'sc2-950bc5.dice.ad.ea.com'
    xbsx: 'sc2-950bc5.dice.ad.ea.com'
    tool: 'sc2-950bc5.dice.ad.ea.com'

test:
  bilbo_url: "https://bilbo-dretest.thor.dice.se/"
  build_share: "\\\\eauk-file.eu.ad.ea.com\\Merlin\\Autobuilds\\test"
  metadata_manager:
    primary:
      name: "bilbo_v2"
      url: "https://mer-bilbo-eck.cobra.dre.ea.com"
      attributes_filename: "build.json"
    secondary:
      - name: "bilbo"
        index: "bilbo_v1"
        url: "https://mer-bilbo-eck.cobra.dre.ea.com"
        attributes_filename: "bilbo_v1.json"
