#Mandatory
default:
  script_path:
    - "TnT\\Bin\\Python\\3\\Lib\\site-packages\\dice_elipy_scripts"
    - "TnT\\Bin\\Python\\virtual\\Lib\\site-packages\\dice_elipy_scripts"
    - "Python\\2.7\\Lib\\site-packages\\dice_elipy_scripts"
    - "Python\\virtual\\Lib\\site-packages\\dice_elipy_scripts"
  project_name: "bct"
  studio_location: "DiceStockholm"
  vault_destination: "\\\\filer.dice.ad.ea.com\\builds\\Vault\\Glacier"
  vault_symstore: "true"
  vault_verification_config_path: "vault_verification_config_bct.yml"
  verify_post_vault: "true"
  ignore_pdb_errors: "true"
  avalanche_symbol_server: ""
  build_share: "\\\\filer.dice.ad.ea.com\\builds\\Battlefield"
  alternate_build_shares:
    bfglacier: "\\\\***********\\builds"
  bilbo_url: "https://bct-bilbo-eck.cobra.dre.ea.com"
  bilbo_api_version: 2
  handle_exe_path: "C:\\ProgramData\\chocolatey\\bin\\handle"
  filer_api_url: "https://it-sweden-api.dice.se/api/v1/smb"
  licensee_code_folder_name: "Code\\DICE"
  md5_exf_path: "C:\\dre\\bin\\exf\\exf.exe"
  linux64_spin_s3_bucket: "ups-dice-glacier-prod-thinclient"
  unified_code_data_stream_project: "true"
  perforce_maxwait: 300
  perforce_retries: 3

  required_vault_files:
    win64:
      - 'bom.fb2'
      - 'ops_chain.zip'
      - 'cas.cat'
      - 'cas_01.cas'
    ps5:
      - 'bom.fb2'
      - 'ops_chain.zip'
      - 'cas.cat'
      - 'cas_01.cas'
    xbsx:
      - 'BattlefieldGame.Main_Xbsx_retail.exe'
      - 'BattlefieldGame.Main_Xbsx_retail.pdb'
      - 'bom.fb2'
      - 'ops_chain.zip'
      - 'cas.cat'
      - 'cas_01.cas'
    server:
      - 'FrostyLogFile.txt'
      - 'build.json'
      - 'builtLevels.json'
    linuxserver:
      - 'BattlefieldGame.Main_Linux64_final_Server.dbg'
      - 'FrostyLogFile.txt'
      - 'build.json'
      - 'builtLevels.json'

  metadata_manager:
    primary:
      name: "bilbo_v2"
      attributes_filename: "build.json"

  # enable_executed_command_log: true
  game_binaries: ['BF2042.exe','BF2042Trial.exe']

  elsa_patch: "true"
  use_onefs_api: "true"
  skip_frosty_game_config_flags: "false"

  metrics_url: "https://dre-metrics-eck.cobra.dre.ea.com"
  metrics_port: 80

  jenkins_metrics_url: "https://dice-metrics-eck.cobra.dre.ea.com"
  jenkins_metrics_port: 443

  oreans:
    artifactory_path: "https://artifacts.at.ea.com/artifactory/dreeu-generic-local/oreans/Virtualizer_3.2.2.0.zip"
    config_file: "TnT\\Code\\DICE\\External\\OreansVirtualizer\\config\\default.cv"

  retention_list:
    - 'code\bct-dev':    1000
    - 'symbols\bct-dev': 1000
  shift_retention: 100
  release_candidate_retention: 2 # 2CLs
  shift_submission_path: "\\\\filer.dice.ad.ea.com\\builds\\Shift\\auto_submissions"
  shift_config_file: "shift_config_bct.yml"
  shift_tool_url: "https://artifacts.at.ea.com/artifactory/list/dreeu-generic-local/shiftsubmission/5.1.0"
  shift_md5_skipped_files:
    - "*.pdb"
    - "*.elf"
    - "*.map"
    - "BattlefieldGame.buildlayout.xml"
    - "FrostyLogFile.txt"
    - "package.mft"
    - "publish.log"
    - "saveicon.png"
    - "StartGame.bat"
  path_retention:
    - \\filer.dice.ad.ea.com\Builds\Battlefield\baselines\BattlefieldGame\CH1-content-dev-disc-build: 3
    - \\filer.dice.ad.ea.com\Builds\Battlefield\baselines\BattlefieldGame\CH1-SP-content-dev-disc-build: 3
    - \\filer.dice.ad.ea.com\Builds\Battlefield\Jukebox\CH1-content-dev: 120
    - \\filer.dice.ad.ea.com\Builds\Battlefield\Jukebox\CH1-marketing-dev: 120
    - \\filer.dice.ad.ea.com\Builds\Battlefield\Jukebox\CH1-stage: 120
    - \\filer.dice.ad.ea.com\Builds\Battlefield\Jukebox\CH1-release: 120
    - \\filer.dice.ad.ea.com\Builds\Battlefield\Jukebox\trunk-content-dev: 120
    - \\filer.dice.ad.ea.com\Builds\Battlefield\offsite\CH1-content-dev: 50
    - \\filer.dice.ad.ea.com\Builds\Battlefield\offsite\CH1-SP-content-dev: 30
    - \\filer.dice.ad.ea.com\Builds\Battlefield\offsite\CH1-SP-stage: 30
    - \\filer.dice.ad.ea.com\Builds\Battlefield\offsite\trunk-content-dev: 30
    - \\filer.dice.ad.ea.com\Builds\Battlefield\offsite_basic\CH1-content-dev: 50
    - \\filer.dice.ad.ea.com\Builds\Battlefield\offsite_basic\CH1-SP-content-dev: 30
    - \\filer.dice.ad.ea.com\Builds\Battlefield\offsite_basic\CH1-SP-stage: 30
    - \\filer.dice.ad.ea.com\Builds\Battlefield\offsite_basic\ecs-splines: 30
    - \\filer.dice.ad.ea.com\Builds\Battlefield\offsite_basic\media-team: 30
    - \\filer.dice.ad.ea.com\Builds\Battlefield\offsite_basic\trunk-content-dev: 30
    - \\filer.dice.ad.ea.com\Builds\Battlefield\offsite_basic_drone\CH1-content-dev: 50
    - \\filer.dice.ad.ea.com\Builds\Battlefield\offsite_basic_drone\CH1-SP-content-dev: 30
    - \\filer.dice.ad.ea.com\Builds\Battlefield\offsite_basic_drone\CH1-SP-content-dev: 30
    - \\filer.dice.ad.ea.com\Builds\Battlefield\offsite_basic_drone\task2: 30
    - \\filer.dice.ad.ea.com\Builds\Battlefield\offsite_basic_drone\trunk-code-dev: 30
    - \\filer.dice.ad.ea.com\Builds\Battlefield\offsite_basic_drone\trunk-content-dev: 30
    - \\filer.dice.ad.ea.com\Builds\Battlefield\PlanA\trunk-content-dev: 30
    - \\filer.dice.ad.ea.com\Builds\Shift\Content_Requests\Glacier\Drone-PlanA\Zipped: 50
    - \\filer.dice.ad.ea.com\Builds\Shift\Content_Requests\Glacier\Drone-PlanA\bf-trunk-editor-release: 10

  azure_path_retention:
  - secret_context: "glacier_azure_fileshare"
    fileshares:
      - fileshare_name: "builds"
        paths:
          - Code/dev-na-to-trunk: 10
          - Code/dev-na-to-trunk-sub: 10
          - Code/trunk-code-dev: 20
          - Code/trunk-content-dev: 10

  # The numbers here for Code are 1 per CL.
  # The numbers for frosty/symbols are 1 per CL.
  # Stream names have to be lower case, since the logic expects this.
  retention_categories:
      frosty\BattlefieldGame:
        - 'default':             5
        - 'ch1-code-dev':       10
        - 'ch1-content-dev':    30
        - 'ch1-stage':          20
        - 'bflabs':              0
        - 'ch1-bflabs-release':  0
        - 'ch1-bflabs-qol':      0
      frosty\Frostbite:
        - 'default':             5
      code:
        - 'default':             40
        - 'ch1-content-dev':    100
        - 'ch1-marketing-dev':  100
        - 'bflabs':               0
        - 'ch1-bflabs-release':   0
        - 'ch1-bflabs-qol':       0
      webexport:
        - 'default':           50
        - 'bflabs':             0
        - 'ch1-bflabs-release': 0
        - 'ch1-bflabs-qol':     0
      expressiondebugdata\BattlefieldGame:
        - 'default': 50
        - 'bflabs':   0
      expressiondebugdata\Frostbite:
        - 'default': 50
      tnt_local:
        - 'default': 0

  spin_retention:
    - 'default': 5

  smoke_retention:
    - 'default': 5

  tasks_to_kill:
    - 'AvalancheCLI.exe'
    - 'msbuild.exe' # should be killed before cl.exe
    - 'cl.exe'
    - 'FrostyIsoTool.exe'
    - 'Icepick.Service.exe'
    - 'mspdbsrv.exe'
    - 'nant.exe'
    - 'orbis-clang.exe'
    - 'orbis-ctrl.exe'
    - 'orbis-pub-cmd.exe'
    - 'orbis-symupload.exe'
    - 'snowcacheserver.exe'
    - 'Tool.Pipeline_Win64_release_Dll.exe'
    - 'vctip.exe'
    - 'animationapp.exe'
    - 'Icepick.exe'
    - 'fbenvcore.exe'
    - 'eapm.exe'

  snowcache_host:
    win64game: 'sc2-95ccdb.dice.ad.ea.com'
    win64trial: 'sc2-95ccdb.dice.ad.ea.com'
    win64server: 'sc2-60a76f.dice.ad.ea.com'
    linux64server: 'sc2-60a76f.dice.ad.ea.com'
    linux64: 'sc2-6485f7.dice.ad.ea.com'
    ps5: 'sc2-763f6a.dice.ad.ea.com'
    xbsx: 'sc2-fd8d6f.dice.ad.ea.com'
    tool: 'sc2-95ccdb.dice.ad.ea.com'

  recompression_cache:
    win64: "kraken-123eba.dice.ad.ea.com"
    server: "kraken-123eba.dice.ad.ea.com"
    xbsx: "kraken-af9d92.dice.ad.ea.com"
    ps5: "kraken-1a2821.dice.ad.ea.com"
    linuxserver: "kraken-4379f0.dice.ad.ea.com"
    linux64: "kraken-4379f0.dice.ad.ea.com"

  symbol_stores_suffix: "SymStore" # defaults to ""

  avalanche:
    avalanche_size: 150 #Size of the avalanche store in GB
    propagate_gets: true #true or false, GET's from upstream
    expiration_time_in_day: 3 #Expiration time in days 3 = never
    #never, monday, tuesday etc
    defrag_day: 'never' #What day a defrag should be done (off so we decide)
    full_defrag_day: 'never' #What day a full defrag should be done (off so we decide)
    maintenance_time_of_day : 9999 #Next maintenance time of day in minutes, set to 9999 so it never runs so only we trigger it.
    maintenance_window_in_minutes : 90 #Maintenance window in minutes.

  avalanche_state_lifetime: # Used by avalanche.remote_clone_db(), but falls back to default value if missing
    default: 3 # Optional because of the fallback
    bct-dev: 1 # Must be integers and are the value of days

  secrets:
    # Get Kingston server connection keys for bundling into Frosty server builds.
    - where:
        build_type: frosty
        platform: server
      url: 'https://int.ess.ea.com'
      namespace: 'eadp-np'
      role_id: '5lkt'
      secret_id_envvar: 'dice-online-gla-prod-secret-id' # Matches a Jenkins Credential
      mount_point: 'approle-identity'
      files:
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_BK_OL_SERVER_20240130_080623'
          key: 'sslKey'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_BK_OL_SERVER_20240130_080623.client.int.eadp.ea.com.key'
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_BK_OL_SERVER_20240130_080623'
          key: 'sslCert'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_BK_OL_SERVER_20240130_080623.client.int.eadp.ea.com.crt'
    # Get Kingston server connection keys for bundling into Frosty linuxserver builds
    - where:
        build_type: frosty
        platform: linuxserver
      url: 'https://int.ess.ea.com'
      namespace: 'eadp-np'
      role_id: '5lkt'
      secret_id_envvar: 'dice-online-gla-prod-secret-id' # Matches a Jenkins Credential
      mount_point: 'approle-identity'
      files:
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_BK_OL_SERVER_20240130_080623'
          key: 'sslKey'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_BK_OL_SERVER_20240130_080623.client.int.eadp.ea.com.key'
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_BK_OL_SERVER_20240130_080623'
          key: 'sslCert'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_BK_OL_SERVER_20240130_080623.client.int.eadp.ea.com.crt'
    - where:
        build_type: frosty
        platform: win64
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/cobra/automation/projects/kingston/certificates'
          key: 'kingston-lic-private.key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Win32\Origin\GAME_OOA\license.key'
    - where:
        build_type: drone
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID' # Matches a Jenkins Credential
      values:
        - '/cobra/automation/shift/automation_credentials'
    - where:
        project_account: true
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/kingston/accounts/main_account'
    - where:
        delete_account: true
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/bct/accounts/main_account'
    - where:
        project_secrets: true
      url: 'https://ess.ea.com'
      namespace:  'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/bct/secrets'
    - where:
        build_type: coverity
      url: 'https://ess.ea.com'
      namespace: 'bct-infrax-battlefield-prod'
      role_id: '91d14ff7-0fed-30c9-71f3-c1878f205bf8'
      secret_id_envvar: 'BCT_SECRETS_SECRET_ID'
      files:
        - path: '/coverity'
          key: 'licensefile'
          to: '{GAME_DATA_DIR}\license.dat'
    # Credentials for pushing to Azure fileshare
    - where:
        glacier_azure_fileshare: true
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/bct/secrets'
    - where:
        build_type: steam
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID' # Matches a Jenkins Credential
      values:
        - '/cobra/automation/services/steam_account'
    # Virtualizer License
    - where:
        oreans_protection: true
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID' # Matches a Jenkins Credential
      values:
        - '/cobra/automation/licenses/oreans_virtualizer'

DiceStockholm:
  build_share: "\\\\filer.dice.ad.ea.com\\builds\\Battlefield"
  studio_location: "DiceStockholm"
  metadata_manager:
    primary:
      name: "bilbo_v2"
      attributes_filename: "build.json"
  spin_retention:
    - 'default': 5
    - 'trunk-code-dev': 5
  smoke_retention:
    - 'default': 5
    - 'trunk-code-dev': 10
    - 'dev-na-to-trunk': 10
  use_onefs_api: "true"

glacier:
  build_share: "\\\\filer.dice.ad.ea.com\\builds\\Battlefield"
  studio_location: "DiceStockholm"
  metadata_manager:
    primary:
      name: "bilbo_v2"
      attributes_filename: "build.json"
  secrets:
    # Get Kingston server connection keys for bundling into Frosty server builds
    - where:
        build_type: frosty
        platform: server
      url: 'https://int.ess.ea.com'
      namespace: 'eadp-np'
      role_id: '5lkt'
      secret_id_envvar: 'dice-online-gla-prod-secret-id' # Matches a Jenkins Credential
      mount_point: 'approle-identity'
      files:
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_BK_OL_SERVER_20240130_080623'
          key: 'sslKey'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_BK_OL_SERVER_20240130_080623.client.int.eadp.ea.com.key'
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_BK_OL_SERVER_20240130_080623'
          key: 'sslCert'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_BK_OL_SERVER_20240130_080623.client.int.eadp.ea.com.crt'
    # Get Kingston server connection keys for bundling into Frosty linuxserver builds
    - where:
        build_type: frosty
        platform: linuxserver
      url: 'https://int.ess.ea.com'
      namespace: 'eadp-np'
      role_id: '5lkt'
      secret_id_envvar: 'dice-online-gla-prod-secret-id' # Matches a Jenkins Credential
      mount_point: 'approle-identity'
      files:
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_BK_OL_SERVER_20240130_080623'
          key: 'sslKey'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_BK_OL_SERVER_20240130_080623.client.int.eadp.ea.com.key'
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_BK_OL_SERVER_20240130_080623'
          key: 'sslCert'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_BK_OL_SERVER_20240130_080623.client.int.eadp.ea.com.crt'
    - where:
        build_type: frosty
        platform: win64
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/cobra/automation/projects/bct/certificates'
          key: 'lic_private_key_glacierskutransition.key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Win32\Origin\GAME_OOA\license.key'
    - where:
        build_type: drone
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID' # Matches a Jenkins Credential
      values:
        - '/cobra/automation/shift/automation_credentials'
    - where:
        project_account: true
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/kingston/accounts/main_account'
    - where:
        delete_account: true
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/bct/accounts/main_account'
    - where:
        project_secrets: true
      url: 'https://ess.ea.com'
      namespace:  'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/bct/secrets'
    - where:
        build_type: coverity
      url: 'https://ess.ea.com'
      namespace: 'bct-infrax-battlefield-prod'
      role_id: '91d14ff7-0fed-30c9-71f3-c1878f205bf8'
      secret_id_envvar: 'BCT_SECRETS_SECRET_ID'
      files:
        - path: '/coverity'
          key: 'licensefile'
          to: '{GAME_DATA_DIR}\license.dat'
    # Credentials for pushing to Azure fileshare
    - where:
        glacier_azure_fileshare: true
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/bct/secrets'
  use_onefs_api: "true"

bflabs:
  build_share: "\\\\filer.dice.ad.ea.com\\builds\\Battlefield"
  spin_s3_bucket: "ups-dice-glacier-production"
  studio_location: "DiceStockholm"
  vault_verification_config_path: "vault_verification_config_bct_bflabs.yml"
  metadata_manager:
    primary:
      name: "bilbo_v2"
      attributes_filename: "build.json"
  secrets:
    # Get Kingston server connection keys for bundling into Frosty server builds
    - where:
        build_type: frosty
        platform: server
      url: 'https://int.ess.ea.com'
      namespace: 'eadp-np'
      role_id: '5lkt'
      secret_id_envvar: 'dice-online-gla-prod-secret-id' # Matches a Jenkins Credential
      mount_point: 'approle-identity'
      files:
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_LABS_BK_OL_SERVER_20240605_170103'
          key: 'sslKey'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_LABS_BK_OL_SERVER_20240605_170103.client.int.eadp.ea.com.key'
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_LABS_BK_OL_SERVER_20240605_170103'
          key: 'sslCert'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_LABS_BK_OL_SERVER_20240605_170103.client.int.eadp.ea.com.crt'
    - where:
        build_type: frosty
        platform: server
      url: 'https://ess.ea.com'
      namespace: 'eadp-prod'
      role_id: '5lkt'
      secret_id_envvar: 'dice-online-gla-prod-secret-prod-id' # Matches a Jenkins Credential
      mount_point: 'approle-identity'
      files:
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_LABS_BK_OL_SERVER_20240712_100412'
          key: 'sslKey'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_LABS_BK_OL_SERVER_20240712_100412.client.prod.eadp.ea.com.key'
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_LABS_BK_OL_SERVER_20240712_100412'
          key: 'sslCert'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_LABS_BK_OL_SERVER_20240712_100412.client.prod.eadp.ea.com.crt'
    # Get Kingston server connection keys for bundling into Frosty linuxserver builds
    - where:
        build_type: frosty
        platform: linuxserver
      url: 'https://int.ess.ea.com'
      namespace: 'eadp-np'
      role_id: '5lkt'
      secret_id_envvar: 'dice-online-gla-prod-secret-id' # Matches a Jenkins Credential
      mount_point: 'approle-identity'
      files:
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_LABS_BK_OL_SERVER_20240605_170103'
          key: 'sslKey'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_LABS_BK_OL_SERVER_20240605_170103.client.int.eadp.ea.com.key'
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_LABS_BK_OL_SERVER_20240605_170103'
          key: 'sslCert'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_LABS_BK_OL_SERVER_20240605_170103.client.int.eadp.ea.com.crt'
    - where:
        build_type: frosty
        platform: linuxserver
      url: 'https://ess.ea.com'
      namespace: 'eadp-prod'
      role_id: '5lkt'
      secret_id_envvar: 'dice-online-gla-prod-secret-prod-id' # Matches a Jenkins Credential
      mount_point: 'approle-identity'
      files:
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_LABS_BK_OL_SERVER_20240712_100412'
          key: 'sslKey'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_LABS_BK_OL_SERVER_20240712_100412.client.prod.eadp.ea.com.key'
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_LABS_BK_OL_SERVER_20240712_100412'
          key: 'sslCert'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_LABS_BK_OL_SERVER_20240712_100412.client.prod.eadp.ea.com.crt'
    - where:
        build_type: frosty
        platform: win64
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/cobra/automation/projects/bct/certificates'
          key: 'lic_private_key_glacierskutransition.key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Win32\Origin\GAME_OOA\license.key'
    - where:
        build_type: drone
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID' # Matches a Jenkins Credential
      values:
        - '/cobra/automation/shift/automation_credentials'
    - where:
        project_account: true
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/kingston/accounts/main_account'
    - where:
        delete_account: true
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/bct/accounts/main_account'
    - where:
        project_secrets: true
      url: 'https://ess.ea.com'
      namespace:  'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/bct/secrets'
    - where:
        build_type: coverity
      url: 'https://ess.ea.com'
      namespace: 'bct-infrax-battlefield-prod'
      role_id: '91d14ff7-0fed-30c9-71f3-c1878f205bf8'
      secret_id_envvar: 'BCT_SECRETS_SECRET_ID'
      files:
        - path: '/coverity'
          key: 'licensefile'
          to: '{GAME_DATA_DIR}\license.dat'
    # Credentials for pushing to Azure fileshare
    - where:
        glacier_azure_fileshare: true
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/bct/secrets'
    - where:
        build_type: steam
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID' # Matches a Jenkins Credential
      values:
        - '/cobra/automation/services/steam_account'
    # Virtualizer License
    - where:
        oreans_protection: true
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID' # Matches a Jenkins Credential
      values:
        - '/cobra/automation/licenses/oreans_virtualizer'
  use_onefs_api: "true"

labs:
  build_share: "\\\\filer.dice.ad.ea.com\\builds\\Battlefield"
  studio_location: "DiceStockholm"
  metadata_manager:
    primary:
      name: "bilbo_v2"
      attributes_filename: "build.json"
  secrets:
    # Get Kingston server connection keys for bundling into Frosty server builds
    - where:
        build_type: frosty
        platform: server
      url: 'https://int.ess.ea.com'
      namespace: 'eadp-np'
      role_id: '5lkt'
      secret_id_envvar: 'dice-online-gla-prod-secret-id' # Matches a Jenkins Credential
      mount_point: 'approle-identity'
      files:
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_LABS_BK_OL_SERVER_20240605_170103'
          key: 'sslKey'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_LABS_BK_OL_SERVER_20240605_170103.client.int.eadp.ea.com.key'
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_LABS_BK_OL_SERVER_20240605_170103'
          key: 'sslCert'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_LABS_BK_OL_SERVER_20240605_170103.client.int.eadp.ea.com.crt'
    - where:
        build_type: frosty
        platform: server
      url: 'https://ess.ea.com'
      namespace: 'eadp-prod'
      role_id: '5lkt'
      secret_id_envvar: 'dice-online-gla-prod-secret-prod-id' # Matches a Jenkins Credential
      mount_point: 'approle-identity'
      files:
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_LABS_BK_OL_SERVER_20240712_100412'
          key: 'sslKey'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_LABS_BK_OL_SERVER_20240712_100412.client.prod.eadp.ea.com.key'
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_LABS_BK_OL_SERVER_20240712_100412'
          key: 'sslCert'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_LABS_BK_OL_SERVER_20240712_100412.client.prod.eadp.ea.com.crt'
    # Get Kingston server connection keys for bundling into Frosty linuxserver builds
    - where:
        build_type: frosty
        platform: linuxserver
      url: 'https://int.ess.ea.com'
      namespace: 'eadp-np'
      role_id: '5lkt'
      secret_id_envvar: 'dice-online-gla-prod-secret-id' # Matches a Jenkins Credential
      mount_point: 'approle-identity'
      files:
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_LABS_BK_OL_SERVER_20240605_170103'
          key: 'sslKey'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_LABS_BK_OL_SERVER_20240605_170103.client.int.eadp.ea.com.key'
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_LABS_BK_OL_SERVER_20240605_170103'
          key: 'sslCert'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_LABS_BK_OL_SERVER_20240605_170103.client.int.eadp.ea.com.crt'
    - where:
        build_type: frosty
        platform: linuxserver
      url: 'https://ess.ea.com'
      namespace: 'eadp-prod'
      role_id: '5lkt'
      secret_id_envvar: 'dice-online-gla-prod-secret-prod-id' # Matches a Jenkins Credential
      mount_point: 'approle-identity'
      files:
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_LABS_BK_OL_SERVER_20240712_100412'
          key: 'sslKey'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_LABS_BK_OL_SERVER_20240712_100412.client.prod.eadp.ea.com.key'
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_LABS_BK_OL_SERVER_20240712_100412'
          key: 'sslCert'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_LABS_BK_OL_SERVER_20240712_100412.client.prod.eadp.ea.com.crt'
    - where:
        build_type: frosty
        platform: win64
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/cobra/automation/projects/bct/certificates'
          key: 'lic_private_key_labs.key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Win32\Origin\GAME_OOA\license_labs.key'
    - where:
        build_type: drone
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID' # Matches a Jenkins Credential
      values:
        - '/cobra/automation/shift/automation_credentials'
    - where:
        project_account: true
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/kingston/accounts/main_account'
    - where:
        delete_account: true
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/bct/accounts/main_account'
    - where:
        project_secrets: true
      url: 'https://ess.ea.com'
      namespace:  'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/bct/secrets'
    - where:
        build_type: coverity
      url: 'https://ess.ea.com'
      namespace: 'bct-infrax-battlefield-prod'
      role_id: '91d14ff7-0fed-30c9-71f3-c1878f205bf8'
      secret_id_envvar: 'BCT_SECRETS_SECRET_ID'
      files:
        - path: '/coverity'
          key: 'licensefile'
          to: '{GAME_DATA_DIR}\license.dat'
    # Credentials for pushing to Azure fileshare
    - where:
        glacier_azure_fileshare: true
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/bct/secrets'
  use_onefs_api: "true"

event:
  build_share: "\\\\eauk-file.eu.ad.ea.com\\Glacier\\Autobuilds"
  shift_submission_path: "\\\\eauk-file.eu.ad.ea.com\\Glacier\\Shift\\auto_submissions"
  studio_location: "Guildford"
  vault_verification_config_path: "vault_verification_config_bct_event.yml"
  metadata_manager:
    primary:
      name: "bilbo_v2"
      attributes_filename: "build.json"
  secrets:
    - where:
        build_type: frosty
        platform: server
      url: 'https://int.ess.ea.com'
      namespace: 'eadp-np'
      role_id: '5lkt'
      secret_id_envvar: 'dice-online-gla-prod-secret-id' # Matches a Jenkins Credential
      mount_point: 'approle-identity'
      files:
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_EVNT_BK_OL_SERVER_20250203_161429'
          key: 'sslKey'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_EVNT_BK_OL_SERVER_20250203_161429.client.int.eadp.ea.com.key'
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_EVNT_BK_OL_SERVER_20250203_161429'
          key: 'sslCert'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_EVNT_BK_OL_SERVER_20250203_161429.client.int.eadp.ea.com.crt'
    - where:
        build_type: frosty
        platform: server
      url: 'https://ess.ea.com'
      namespace: 'eadp-prod'
      role_id: '5lkt'
      secret_id_envvar: 'dice-online-gla-prod-secret-prod-id' # Matches a Jenkins Credential
      mount_point: 'approle-identity'
      files:
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_EVNT_BK_OL_SERVER_20250321_121230'
          key: 'sslKey'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_EVNT_BK_OL_SERVER_20250321_121230.client.prod.eadp.ea.com.key'
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_EVNT_BK_OL_SERVER_20250321_121230'
          key: 'sslCert'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_EVNT_BK_OL_SERVER_20250321_121230.client.prod.eadp.ea.com.crt'
    - where:
        build_type: frosty
        platform: linuxserver
      url: 'https://int.ess.ea.com'
      namespace: 'eadp-np'
      role_id: '5lkt'
      secret_id_envvar: 'dice-online-gla-prod-secret-id' # Matches a Jenkins Credential
      mount_point: 'approle-identity'
      files:
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_EVNT_BK_OL_SERVER_20250203_161429'
          key: 'sslKey'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_EVNT_BK_OL_SERVER_20250203_161429.client.int.eadp.ea.com.key'
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_EVNT_BK_OL_SERVER_20250203_161429'
          key: 'sslCert'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_EVNT_BK_OL_SERVER_20250203_161429.client.int.eadp.ea.com.crt'
    - where:
        build_type: frosty
        platform: linuxserver
      url: 'https://ess.ea.com'
      namespace: 'eadp-prod'
      role_id: '5lkt'
      secret_id_envvar: 'dice-online-gla-prod-secret-prod-id' # Matches a Jenkins Credential
      mount_point: 'approle-identity'
      files:
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_EVNT_BK_OL_SERVER_20250321_121230'
          key: 'sslKey'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_EVNT_BK_OL_SERVER_20250321_121230.client.prod.eadp.ea.com.key'
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_EVNT_BK_OL_SERVER_20250321_121230'
          key: 'sslCert'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_EVNT_BK_OL_SERVER_20250321_121230.client.prod.eadp.ea.com.crt'
    - where:
        build_type: frosty
        platform: win64
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/cobra/automation/projects/bct/certificates'
          key: 'lic_private_key_glacierskutransition.key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Win32\Origin\GAME_OOA\license.key'
    - where:
        build_type: drone
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID' # Matches a Jenkins Credential
      values:
        - '/cobra/automation/shift/automation_credentials'
    - where:
        project_account: true
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/kingston/accounts/main_account'
    - where:
        delete_account: true
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/bct/accounts/main_account'
    - where:
        project_secrets: true
      url: 'https://ess.ea.com'
      namespace:  'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/bct/secrets'
    - where:
        build_type: coverity
      url: 'https://ess.ea.com'
      namespace: 'bct-infrax-battlefield-prod'
      role_id: '91d14ff7-0fed-30c9-71f3-c1878f205bf8'
      secret_id_envvar: 'BCT_SECRETS_SECRET_ID'
      files:
        - path: '/coverity'
          key: 'licensefile'
          to: '{GAME_DATA_DIR}\license.dat'
    # Credentials for pushing to Azure fileshare
    - where:
        glacier_azure_fileshare: true
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/bct/secrets'
    - where:
        build_type: steam
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID' # Matches a Jenkins Credential
      values:
        - '/cobra/automation/services/steam_account'
    # Virtualizer License
    - where:
        oreans_protection: true
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID' # Matches a Jenkins Credential
      values:
        - '/cobra/automation/licenses/oreans_virtualizer'
  use_onefs_api: "true"

skybuild:
  build_share: "\\\\filer.dice.ad.ea.com\\builds\\Battlefield"
  alternate_build_shares:
    bfglacier_azure: "\\\\bfglacier.file.core.windows.net\\builds"
  studio_location: "Cloud"
  metadata_manager:
    primary:
      name: "bilbo_v2"
      index: "skybuild_bilbo_v2"
      attributes_filename: "build.json"

RippleEffect:
  build_share: "\\\\dice-la.la.ad.ea.com\\Builds\\Battlefield"
  shift_submission_path: "\\\\dice-la.la.ad.ea.com\\Builds\\Shift\\auto_submissions"
  shift_retention: 100
  studio_location: "RippleEffect"
  use_onefs_api: "false"
  alternate_build_shares:
    offsite_basic_drone: "\\\\dice-la.la.ad.ea.com\\Builds\\External\\Battlefield"
    bfglacier: "\\\\***********\\builds"
  bilbo_url: "https://bct-bilbo-eck.cobra.dre.ea.com"
  metadata_manager:
    primary:
      name: "bilbo"
      index: "rippleeffect_bilbo"
      url: "https://bct-bilbo-eck.cobra.dre.ea.com"
      attributes_filename: "build.json"

  path_retention:
    - not\a\real\path: 9001

  retention_categories:
    code:
      - 'default': 100
      - 'CH1-code-dev': 140
      - 'CH1-content-dev': 140
      - 'CH1-SP-content-dev': 140
      - 'CH1-stage': 140
      - 'CH1-marketing-dev': 100
    drone:
      - 'default': 100
      - 'trunk-code-dev': 140
      - 'trunk-content-dev': 140
    frosty\BattlefieldGame:
      - 'default': 40
      - 'CH1-playtest-gnt-na': 40
      - '2024_1_dev-bf-to-CH1': 40

RippleEffect_Outsourcers:
  build_share: "\\\\dice-la.la.ad.ea.com\\Builds\\External\\Battlefield"
  bilbo_url: "http://eala-kin-bilbo.la.ad.ea.com:9200/"
  use_onefs_api: "false"
  studio_location: "RippleEffect"
  metadata_manager:
    primary:
      name: "bilbo_v2"
      attributes_filename: "build.json"
    secondary:
      - name: "bilbo"
        index: "bilbo_v1"
        url: "http://eala-kin-bilbo.la.ad.ea.com:9200/"
        attributes_filename: "bilbo_v1.json"

  path_retention:
    - \\dice-la.la.ad.ea.com\Builds\External\Battlefield\trunk-content-dev: 50

  retention_categories:
    drone:
      - 'default': 50

Task13:
  build_share: "\\\\dice-la.la.ad.ea.com\\Builds\\Battlefield"
  shift_submission_path: "\\\\dice-la.la.ad.ea.com\\Builds\\Shift\\auto_submissions"
  studio_location: "RippleEffect"
  use_onefs_api: "false"
  alternate_build_shares:
    offsite_basic_drone: "\\\\dice-la.la.ad.ea.com\\Builds\\External\\Battlefield"
    bfglacier: "\\\\***********\\builds"
  bilbo_url: "http://eala-kin-bilbo.la.ad.ea.com:9200/"
  linux64_spin_s3_bucket: "ups-respawn-apex-staging/uploads"
  spin_s3_bucket: "ups-respawn-apex-staging/uploads"
  spin_s3_role: "arn:aws:iam::494057277917:role/ups-respawn-apex-staging-client-upload"
  metadata_manager:
    primary:
      name: "bilbo_v2"
      attributes_filename: "build.json"
    secondary:
      - name: "bilbo"
        index: "bilbo_v1"
        url: "http://eala-kin-bilbo.la.ad.ea.com:9200"
        attributes_filename: "bilbo_v1.json"
  secrets:
    - where:
        project_secrets: true
      url: 'https://ess.ea.com'
      namespace:  'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/task13/secrets'
    # Get Kingston server connection keys for bundling into Frosty server builds.
    - where:
        build_type: frosty
        platform: server
      url: 'https://int.ess.ea.com'
      namespace: 'eadp-np'
      role_id: '5lkt'
      secret_id_envvar: 'dice-online-gla-prod-secret-id' # Matches a Jenkins Credential
      mount_point: 'approle-identity'
      files:
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_BK_OL_SERVER_20240130_080623'
          key: 'sslKey'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_BK_OL_SERVER_20240130_080623.client.int.eadp.ea.com.key'
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_BK_OL_SERVER_20240130_080623'
          key: 'sslCert'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_BK_OL_SERVER_20240130_080623.client.int.eadp.ea.com.crt'
    # Get Kingston server connection keys for bundling into Frosty linuxserver builds
    - where:
        build_type: frosty
        platform: linuxserver
      url: 'https://int.ess.ea.com'
      namespace: 'eadp-np'
      role_id: '5lkt'
      secret_id_envvar: 'dice-online-gla-prod-secret-id' # Matches a Jenkins Credential
      mount_point: 'approle-identity'
      files:
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_BK_OL_SERVER_20240130_080623'
          key: 'sslKey'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_BK_OL_SERVER_20240130_080623.client.int.eadp.ea.com.key'
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_BK_OL_SERVER_20240130_080623'
          key: 'sslCert'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_BK_OL_SERVER_20240130_080623.client.int.eadp.ea.com.crt'

  path_retention:
    - not\a\real\path: 9001

  retention_categories:
    code:
      - 'default': 100
    drone:
      - 'default': 100

earo:
  build_share: "\\\\ro-nas-dice.eamobile.ad.ea.com\\sync"
  bilbo_url: "https://bct-bilbo-eck.cobra.dre.ea.com"
  studio_location: "EARO"
  metadata_manager:
    primary:
      name: "bilbo"
      index: "earo_bilbo"
      url: "https://bct-bilbo-eck.cobra.dre.ea.com"
      attributes_filename: "earo_bilbo.json"

  retention_categories:
    code:
      - 'default': 20
      - 'trunk-code-dev': 10
      - 'dev-na-to-trunk': 5
      - 'dev-na-to-trunk-sub': 5
      - 'trunk-content-dev': 5
      - 'CH1-content-dev': 25
      - 'CH1-SP-content-dev': 5
      - 'CH1-stage': 5
      - '2024_1_dev-bf-to-CH1': 5

Montreal:
  build_share: "\\\\eam-fs1.eam.ad.ea.com\\Motive_Montreal\\Projects\\Glacier\\Builds"
  bilbo_url: "https://bct-bilbo-eck.cobra.dre.ea.com"
  studio_location: "Montreal"
  metadata_manager:
    primary:
      name: "bilbo"
      index: "montreal_bilbo"
      url: "https://bct-bilbo-eck.cobra.dre.ea.com"
      attributes_filename: "montreal_bilbo.json"

  retention_categories:
    code:
      - 'default': 20
      - 'CH1-code-dev': 20
      - 'CH1-content-dev': 100
      - 'CH1-marketing-dev': 20
      - 'CH1-qol': 20
      - 'CH1-release': 20
      - 'CH1-SP-content-dev': 5
      - 'CH1-stage': 100
      - 'task1': 10
      - 'trunk-content-dev': 20
      - 'trunk-code-dev': 20

Guildford:
  build_share: "\\\\eauk-file.eu.ad.ea.com\\Glacier\\Autobuilds"
  bilbo_url: "https://bct-bilbo-eck.cobra.dre.ea.com"
  shift_submission_path: "\\\\eauk-file.eu.ad.ea.com\\Glacier\\Shift\\auto_submissions"
  shift_retention: 10
  studio_location: "Guildford"
  use_onefs_api: "false"
  metadata_manager:
    primary:
      name: "bilbo"
      index: "criterion_bilbo"
      url: "https://bct-bilbo-eck.cobra.dre.ea.com"
      attributes_filename: "build.json"

  retention_categories:
    code:
      - 'default': 10
      - 'trunk-content-dev-test': 0
      - 'trunk-content-dev': 5
      - 'criterion-content-warm': 5
    tnt_local:
      - 'default': 0
    frosty\BattlefieldGame:
      - 'default': 8
      - 'CH1-event-release': 12
      - 'CH1-release': 35
      - 'CH1-SP-release': 10
      - 'CH1-bflabs-release': 10
      - 'CH1-qol': 10
      - 'CH1-event': 10
      - 'CH1-bflabs-qol': 10

  path_retention:
    - \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\offsite_basic\trunk-code-dev: 40
    - \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\offsite_basic_drone\trunk-code-dev: 40

bflabs-criterion:
  build_share: "\\\\eauk-file.eu.ad.ea.com\\Glacier\\Autobuilds"
  shift_submission_path: "\\\\eauk-file.eu.ad.ea.com\\Glacier\\Shift\\auto_submissions"
  spin_s3_bucket: "ups-dice-glacier-production"
  studio_location: "Guildford"
  metadata_manager:
    primary:
      name: "bilbo_v2"
      attributes_filename: "build.json"
  secrets:
    - where:
        build_type: frosty
        platform: server
      url: 'https://int.ess.ea.com'
      namespace: 'eadp-np'
      role_id: '5lkt'
      secret_id_envvar: 'dice-online-gla-prod-secret-id' # Matches a Jenkins Credential
      mount_point: 'approle-identity'
      files:
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_LABS_BK_OL_SERVER_20240605_170103'
          key: 'sslKey'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_LABS_BK_OL_SERVER_20240605_170103.client.int.eadp.ea.com.key'
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_LABS_BK_OL_SERVER_20240605_170103'
          key: 'sslCert'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_LABS_BK_OL_SERVER_20240605_170103.client.int.eadp.ea.com.crt'
    - where:
        build_type: frosty
        platform: server
      url: 'https://ess.ea.com'
      namespace: 'eadp-prod'
      role_id: '5lkt'
      secret_id_envvar: 'dice-online-gla-prod-secret-prod-id' # Matches a Jenkins Credential
      mount_point: 'approle-identity'
      files:
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_LABS_BK_OL_SERVER_20240712_100412'
          key: 'sslKey'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_LABS_BK_OL_SERVER_20240712_100412.client.prod.eadp.ea.com.key'
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_LABS_BK_OL_SERVER_20240712_100412'
          key: 'sslCert'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_LABS_BK_OL_SERVER_20240712_100412.client.prod.eadp.ea.com.crt'
    # Get Kingston server connection keys for bundling into Frosty linuxserver builds
    - where:
        build_type: frosty
        platform: linuxserver
      url: 'https://int.ess.ea.com'
      namespace: 'eadp-np'
      role_id: '5lkt'
      secret_id_envvar: 'dice-online-gla-prod-secret-id' # Matches a Jenkins Credential
      mount_point: 'approle-identity'
      files:
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_LABS_BK_OL_SERVER_20240605_170103'
          key: 'sslKey'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_LABS_BK_OL_SERVER_20240605_170103.client.int.eadp.ea.com.key'
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_LABS_BK_OL_SERVER_20240605_170103'
          key: 'sslCert'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_LABS_BK_OL_SERVER_20240605_170103.client.int.eadp.ea.com.crt'
    - where:
        build_type: frosty
        platform: linuxserver
      url: 'https://ess.ea.com'
      namespace: 'eadp-prod'
      role_id: '5lkt'
      secret_id_envvar: 'dice-online-gla-prod-secret-prod-id' # Matches a Jenkins Credential
      mount_point: 'approle-identity'
      files:
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_LABS_BK_OL_SERVER_20240712_100412'
          key: 'sslKey'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_LABS_BK_OL_SERVER_20240712_100412.client.prod.eadp.ea.com.key'
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_LABS_BK_OL_SERVER_20240712_100412'
          key: 'sslCert'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_LABS_BK_OL_SERVER_20240712_100412.client.prod.eadp.ea.com.crt'
    - where:
        build_type: frosty
        platform: win64
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/cobra/automation/projects/bct/certificates'
          key: 'lic_private_key_glacierskutransition.key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Win32\Origin\GAME_OOA\license.key'
    - where:
        build_type: drone
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID' # Matches a Jenkins Credential
      values:
        - '/cobra/automation/shift/automation_credentials'
    # Virtualizer License
    - where:
        oreans_protection: true
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID' # Matches a Jenkins Credential
      values:
        - '/cobra/automation/licenses/oreans_virtualizer'
    - where:
        build_type: steam
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID' # Matches a Jenkins Credential
      values:
        - '/cobra/automation/services/steam_account'
    - where:
        project_secrets: true
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/bct/secrets'


