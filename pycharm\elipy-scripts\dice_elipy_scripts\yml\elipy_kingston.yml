# Mandatory
default:
  script_path:
    - "TnT\\Bin\\Python\\3\\Lib\\site-packages\\dice_elipy_scripts"
    - "TnT\\Bin\\Python\\virtual\\Lib\\site-packages\\dice_elipy_scripts"
    - "Python\\2.7\\Lib\\site-packages\\dice_elipy_scripts"
    - "Python\\virtual\\Lib\\site-packages\\dice_elipy_scripts"
  project_name: "kingston"
  studio_location: "DiceStockholm"
  avalanche_symbol_server: "dice-cas-sym.dice.ad.ea.com"
  recompression_cache:
    win64: "kraken-123eba.dice.ad.ea.com"
    server: "kraken-123eba.dice.ad.ea.com"
    xb1: "kraken-8a5a05.dice.ad.ea.com"
    xbsx: "kraken-af9d92.dice.ad.ea.com"
    ps4: "kraken-60a94a.dice.ad.ea.com"
    ps5: "kraken-1a2821.dice.ad.ea.com"
    linuxserver: "kraken-4379f0.dice.ad.ea.com"
  symbol_stores_suffix: "SymStore"
  vault_destination: "\\\\filer.dice.ad.ea.com\\builds\\Vault\\Kingston"
  vault_symstore: "true"
  vault_verification_config_path: "vault_verification_config_kingston.yml"
  ant_local_dir: "ANT_Source"
  metadata_files:
    - "AutoIntegrate.json"
  bilbo_api_version: 2
  bilbo_url: "https://dice-kin-bilbo-eck.cobra.dre.ea.com"
  build_share: "\\\\filer.dice.ad.ea.com\\builds\\Kingston"
  alternate_build_shares:
    bfkingston: "\\\\***********\\builds"

  metadata_manager:
    primary:
      name: "bilbo_v2"
      url: "https://dice-kin-bilbo-eck.cobra.dre.ea.com"
      attributes_filename: "build.json"
    secondary:
      - name: "bilbo"
        index: "bilbo_v1"
        url: "https://dice-kin-bilbo-eck.cobra.dre.ea.com"
        attributes_filename: "bilbo_v1.json"

  filer_api_url: "https://it-sweden-api.dice.se/api/v1/smb"

  licensee_code_folder_name: "Code\\DICE"
  md5_exf_path: "C:\\dre\\bin\\exf\\exf.exe"

  metrics_url: "https://dre-metrics-eck.cobra.dre.ea.com"
  metrics_port: 80

  jenkins_metrics_url: "https://dice-metrics-eck.cobra.dre.ea.com"
  jenkins_metrics_port: 443

  denuvo:
    artifactory_path: "https://artifacts.at.ea.com/artifactory/dreeu-generic-local/denuvo/kingston/denuvo_files.zip"
    project:
      retail: 'Battlefield Kingston - Retail - V3'
      trial: 'Battlefield Kingston - Trial - V3'
    servers: ''
    server_url: 'https://oetk2-prod.codefusion.technology'
  game_binaries: [ 'BF2042.exe','BF2042Trial.exe' ]
  # ps4_disk_code_branch: bf-release-ps4
  # ps4_disk_code_changelist: 4369642
  # ps4_disk_data_branch: bf-release-ps4
  # ps4_disk_data_changelist: 3837595

  elsa_patch: "true"
  use_onefs_api: "false"
  skip_frosty_game_config_flags: "false"

  required_vault_files:
    #win64:
    #  - 'bfv.exe'
    #  - 'bfvTrial.exe'
    #  - 'bom.fb2'
    #  - 'ops_chain.zip'
    #  - 'cas.cat'
    #  - 'cas_01.cas'
    ps4:
      - 'bom.fb2'
      - 'ops_chain.zip'
      - 'cas.cat'
      - 'cas_01.cas'
    ps5:
      - 'bom.fb2'
      - 'ops_chain.zip'
      - 'cas.cat'
      - 'cas_01.cas'
    xb1:
      - 'appxmanifest.xml'
      - 'bom.fb2'
      - 'ops_chain.zip'
      - 'cas.cat'
      - 'cas_01.cas'
    xbsx:
      - 'bom.fb2'
      - 'ops_chain.zip'
      - 'cas.cat'
      - 'cas_01.cas'
    #server:
    #  - 'Casablanca.Main_Win64_final_Server.exe'


  # Avalanche Settings
  avalanche:
    avalanche_size: 150 # Size of the Avalanche store in GB
    propagate_gets: true # true or false, GET's from upstream
    expiration_time_in_day: 3 # Expiration time in days 3 = never

    # never, monday, tuesday etc
    defrag_day: 'never' # What day a defrag should be done (off so we decide)
    full_defrag_day: 'never' # What day a full defrag should be done (off so we decide)

    maintenance_time_of_day: 9999 # Next maintenance time of day in minutes, set to 9999 so it never runs so only we trigger it.
    maintenance_window_in_minutes: 90 # Maintenance window in minutes.

  avalanche_state_lifetime: # Used by avalanche.remote_clone_db(), but falls back to default value if missing
    default: 3 # Optional because of the fallback
    kin-dev: 1 # Must be integers and are the value of days
    kin-dev-unverified: 1

  # The numbers here for Code are 1 per CL.
  # The numbers for frosty/symbols are 1 per CL.
  retention_categories:
    code:
      - 'default':            25
      - 'kin-dev-unverified': 40
      - 'kin-release':        40
      - 'kin-stage':          40
    code_nomaster:
      - 'default': 0
    tnt_local:
      - 'default': 0
    frosty\BattlefieldGame: #based on forecast https://docs.google.com/spreadsheets/d/1q-QloqrqDyCd_vseZGtxk-Q12irS8I_nejULAvVd1zA/edit#gid=0
      - 'default':             5
      - 'future-dev-content': 15
      - 'kin-dev':            15
      - 'kin-dev-unverified': 15
      - 'kin-live':           10
      - 'kin-release':        30
      - 'kin-stage':          15
    frosty\Frostbite: #based on forecast https://docs.google.com/spreadsheets/d/1q-QloqrqDyCd_vseZGtxk-Q12irS8I_nejULAvVd1zA/edit#gid=0
      - 'default': 10
    symbols:
      - 'default': 20
    avalanchestate:
      - 'default': 0
    ant_cache:
      - 'default': 0
    webexport:
      - 'default': 50
    expressiondebugdata\BattlefieldGame:
      - 'default': 50
    expressiondebugdata\Frostbite:
      - 'default': 50

  path_retention:
    - \\filer.dice.ad.ea.com\builds\Kingston\baselines\BattlefieldGame\kin-dev: 3
    - \\filer.dice.ad.ea.com\builds\Kingston\crashdumps: 30
    - \\filer.dice.ad.ea.com\builds\Kingston\crashdumps\pipeline_crashdumps: 100
    - \\filer.dice.ad.ea.com\Builds\Kingston\Jukebox\kin-dev: 30
    - \\filer.dice.ad.ea.com\Builds\Kingston\offsite\kin-dev: 30
    - \\filer.dice.ad.ea.com\Builds\Kingston\offsite_basic\kin-dev: 30
    - \\filer.dice.ad.ea.com\Builds\Kingston\offsite_basic\kin-stage: 30
    - \\filer.dice.ad.ea.com\Builds\Kingston\offsite_basic\kin-release: 30
    - \\filer.dice.ad.ea.com\Builds\Kingston\offsite_basic_drone\kin-dev: 30
    - \\filer.dice.ad.ea.com\Builds\Kingston\offsite_basic_drone\kin-stage: 30
    - \\filer.dice.ad.ea.com\builds\Kingston\PublishedBuilds: 20
    - \\filer.dice.ad.ea.com\Builds\Shift\Content_Requests\Kingston\Drone-Adia: 20
    - \\filer.dice.ad.ea.com\Builds\Shift\Content_Requests\Kingston\Drone-ElectricSquare: 20
    - \\filer.dice.ad.ea.com\Builds\Shift\Content_Requests\Kingston\Drone-Elite3D: 20
    - \\filer.dice.ad.ea.com\Builds\Shift\Content_Requests\Kingston\Drone-Globant: 20
    - \\filer.dice.ad.ea.com\Builds\Shift\Content_Requests\Kingston\Drone-Keywords: 20
    - \\filer.dice.ad.ea.com\Builds\Shift\Content_Requests\Kingston\Drone-MineLoader: 20
    - \\filer.dice.ad.ea.com\Builds\Shift\Content_Requests\Kingston\Drone-OF3D: 20
    - \\filer.dice.ad.ea.com\Builds\Shift\Content_Requests\Kingston\Drone-Sperasoft: 20
    - \\filer.dice.ad.ea.com\Builds\Shift\Content_Requests\Kingston\Drone-Winking: 20

  azure_path_retention:
  - secret_context: "kingston_azure_fileshare"
    fileshares:
      - fileshare_name: "builds"
        paths:
          - Code/kin-dev-unverified: 15

  spin_retention:
    - 'default': 5

  smoke_retention:
    - 'default': 5

  shift_retention: 80
  release_candidate_retention: 2 # 2 CLs
  shift_submission_path: "\\\\filer.dice.ad.ea.com\\builds\\Shift\\auto_submissions"
  shift_config_file: "shift_config_kingston.yml"
  shift_tool_url: "https://artifacts.at.ea.com/artifactory/list/dreeu-generic-local/shiftsubmission/5.1.0"
  shift_md5_skipped_files:
    - "*.pdb"
    - "*.elf"
    - "*.map"
    - "BattlefieldGame.buildlayout.xml"
    - "FrostyLogFile.txt"
    - "package.mft"
    - "publish.log"
    - "saveicon.png"
    - "StartGame.bat"
  tasks_to_kill:
    - 'AvalancheCLI.exe'
    - 'msbuild.exe' # should be killed before cl.exe
    - 'cl.exe'
    - 'FrostyIsoTool.exe'
    - 'Icepick.Service.exe'
    - 'mspdbsrv.exe'
    - 'nant.exe'
    - 'orbis-clang.exe'
    - 'orbis-ctrl.exe'
    - 'orbis-pub-cmd.exe'
    - 'orbis-symupload.exe'
    - 'snowcacheserver.exe'
    - 'Tool.Pipeline_Win64_release_Dll.exe'
    - 'vctip.exe'
    - 'animationapp.exe'
    - 'FrostbiteDatabaseUpgrader.exe'
    - 'Icepick.exe'
    - 'eapm.exe'

  secrets:
    # Get Kingston server connection keys for bundling into Frosty server builds
    - where:
        build_type: frosty
        platform: server
      url: 'https://ess.ea.com'
      namespace: 'dice-online-casablanca-prod'
      role_id: '0feab1e9-ba7e-acaa-610e-4365569bceb6'
      secret_id_envvar: 'GAME_TEAM_SECRETS_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/bf21/KINGSTON_BK_OL_SERVER.client.int.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.int.eadp.ea.com.key'
        - path: '/bf21/KINGSTON_BK_OL_SERVER.client.int.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.int.eadp.ea.com.crt'
        - path: '/bf21/KINGSTON_BK_OL_SERVER.client.prod.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.prod.eadp.ea.com.key'
        - path: '/bf21/KINGSTON_BK_OL_SERVER.client.prod.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.prod.eadp.ea.com.crt'
    # Get Kingston server connection keys for bundling into Frosty linuxserver builds
    - where:
        build_type: frosty
        platform: linuxserver
      url: 'https://ess.ea.com'
      namespace: 'dice-online-casablanca-prod'
      role_id: '0feab1e9-ba7e-acaa-610e-4365569bceb6'
      secret_id_envvar: 'GAME_TEAM_SECRETS_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/bf21/KINGSTON_BK_OL_SERVER.client.int.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.int.eadp.ea.com.key'
        - path: '/bf21/KINGSTON_BK_OL_SERVER.client.int.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.int.eadp.ea.com.crt'
        - path: '/bf21/KINGSTON_BK_OL_SERVER.client.prod.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.prod.eadp.ea.com.key'
        - path: '/bf21/KINGSTON_BK_OL_SERVER.client.prod.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.prod.eadp.ea.com.crt'
    # Get Kingston Origin & EasyAntiCheat (EAC) key for bundling into Frosty PC builds
    - where:
        build_type: frosty
        platform: win64
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/cobra/automation/projects/kingston/certificates'
          key: 'kingston-lic-private.key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Win32\Origin\GAME_OOA\license.key'
        - path: '/cobra/automation/projects/kingston/certificates'
          key: 'lic-private-publicplaytest.key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Win32\Origin\GAME_OOA\license_publicplaytest.key'
        - path: '/cobra/automation/projects/kingston/certificates'
          key: 'lic-private-kingston-tech-test.key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Win32\Origin\GAME_OOA\license_alpha.key'
        - path: '/cobra/automation/projects/kingston/certificates'
          key: 'kingston-lic-private-trial.key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Win32\Origin\GAME_OOA\trial_license.key'
        - path: '/cobra/automation/projects/kingston/certificates'
          key: 'kingston-lic-private-review.key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Win32\Origin\GAME_OOA\review_license.key'
        - path: '/cobra/automation/projects/kingston/certificates'
          key: 'kingston-game-private.key'
          to: '{TNT_ROOT}\Code\Extension\EasyAntiCheat\HashTool\game_private.key'
        - path: '/cobra/automation/projects/kingston/certificates'
          key: 'kingston-game-public-out-binary.cer'
          to: '{TNT_ROOT}\Code\Extension\EasyAntiCheat\HashTool\game_public.cer'
    - where:
        build_type: drone
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID' # Matches a Jenkins Credential
      values:
        - '/cobra/automation/shift/automation_credentials'
    - where:
        project_account: true
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/kingston/accounts/main_account'
    - where:
        project_secrets: true
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/kingston/secrets'
      # Credentials for pushing to Azure fileshare
    - where:
        kingston_azure_fileshare: true
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/kingston/secrets'
  snowcache_host:
    win64game: 'sc2-16cb21.dice.ad.ea.com'
    win64trial: 'sc2-16cb21.dice.ad.ea.com'
    win64server: 'sc2-60a76f.dice.ad.ea.com'
    linux64server: 'sc2-60a76f.dice.ad.ea.com'
    linux64: 'sc2-6485f7.dice.ad.ea.com'
    ps4: 'sc2-3addd8.dice.ad.ea.com'
    ps5: 'sc2-763f6a.dice.ad.ea.com'
    xb1: 'sc2-99528e.dice.ad.ea.com'
    xb1gdk: 'sc2-99528e.dice.ad.ea.com'
    xbsx: 'sc2-686d25.dice.ad.ea.com'
    tool: 'sc2-6485f7.dice.ad.ea.com'

dice:
  build_share: "\\\\filer.dice.ad.ea.com\\builds\\Kingston"

test:
  bilbo_url: "https://bilbo-dretest.thor.dice.se/"
  build_share: "\\\\filer.dice.ad.ea.com\\builds\\Kingston\\test"

irt:
  secrets:
    # Get Kingston server connection keys for bundling into Frosty server builds
    - where:
        build_type: frosty
        platform: server
      url: 'https://ess.ea.com'
      namespace: 'dice-online-casablanca-prod'
      role_id: '0feab1e9-ba7e-acaa-610e-4365569bceb6'
      secret_id_envvar: 'GAME_TEAM_SECRETS_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/bf21/KINGSTON_QVT_BK_OL_SERVER.client.int.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.int.eadp.ea.com.key'
        - path: '/bf21/KINGSTON_QVT_BK_OL_SERVER.client.int.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.int.eadp.ea.com.crt'
        - path: '/bf21/KINGSTON_QVT_BK_OL_SERVER.client.prod.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.prod.eadp.ea.com.key'
        - path: '/bf21/KINGSTON_QVT_BK_OL_SERVER.client.prod.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.prod.eadp.ea.com.crt'
    # Get Kingston server connection keys for bundling into Frosty linuxserver builds
    - where:
        build_type: frosty
        platform: linuxserver
      url: 'https://ess.ea.com'
      namespace: 'dice-online-casablanca-prod'
      role_id: '0feab1e9-ba7e-acaa-610e-4365569bceb6'
      secret_id_envvar: 'GAME_TEAM_SECRETS_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/bf21/KINGSTON_QVT_BK_OL_SERVER.client.int.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.int.eadp.ea.com.key'
        - path: '/bf21/KINGSTON_QVT_BK_OL_SERVER.client.int.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.int.eadp.ea.com.crt'
        - path: '/bf21/KINGSTON_QVT_BK_OL_SERVER.client.prod.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.prod.eadp.ea.com.key'
        - path: '/bf21/KINGSTON_QVT_BK_OL_SERVER.client.prod.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.prod.eadp.ea.com.crt'
    # Get Kingston Origin keys for bundling into Frosty PC builds
    - where:
        build_type: frosty
        platform: win64
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/cobra/automation/projects/kingston/certificates'
          key: 'irt-lic-private.key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Win32\Origin\GAME_OOA\license.key'
        - path: '/cobra/automation/projects/kingston/certificates'
          key: 'irt-lic-private.key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Win32\Origin\GAME_OOA\trial_license.key'
    - where:
        project_secrets: true
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/kingston/secrets'

review:
  build_share: "\\\\filer.dice.ad.ea.com\\builds\\Kingston"
  denuvo:
    artifactory_path: "https://artifacts.at.ea.com/artifactory/dreeu-generic-local/denuvo/kingston/denuvo_files.zip"
    project:
      retail: 'Battlefield Kingston - Review - V2'
      trial: 'Battlefield Kingston - Trial - V2'
    servers: ''
    server_url: 'https://oetk2-prod.codefusion.technology'
  game_binaries: [ 'BF2042.exe','BF2042Trial.exe' ]

demo:
  denuvo:
    artifactory_path: "https://artifacts.at.ea.com/artifactory/dreeu-generic-local/denuvo/kingston/denuvo_files.zip"
    project:
      retail: 'Battlefield Kingston - Alpha'
      trial: 'Battlefield Kingston - Trial'
    servers: ''
    server_url: 'https://oetk2-prod.codefusion.technology'
  secrets:
    # Get Kingston server connection keys for bundling into Frosty server builds
    - where:
        build_type: frosty
        platform: server
      url: 'https://ess.ea.com'
      namespace: 'dice-online-casablanca-prod'
      role_id: '0feab1e9-ba7e-acaa-610e-4365569bceb6'
      secret_id_envvar: 'GAME_TEAM_SECRETS_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/bf21/KINGSTON_DEMO_BK_OL_SERVER.client.int.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_DEMO_BK_OL_SERVER.client.int.eadp.ea.com.key'
        - path: '/bf21/KINGSTON_DEMO_BK_OL_SERVER.client.int.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_DEMO_BK_OL_SERVER.client.int.eadp.ea.com.crt'
        - path: '/bf21/KINGSTON_DEMO_BK_OL_SERVER.client.prod.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_DEMO_BK_OL_SERVER.client.prod.eadp.ea.com.key'
        - path: '/bf21/KINGSTON_DEMO_BK_OL_SERVER.client.prod.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_DEMO_BK_OL_SERVER.client.prod.eadp.ea.com.crt'
    # Get Kingston server connection keys for bundling into Frosty linuxserver builds
    - where:
        build_type: frosty
        platform: linuxserver
      url: 'https://ess.ea.com'
      namespace: 'dice-online-casablanca-prod'
      role_id: '0feab1e9-ba7e-acaa-610e-4365569bceb6'
      secret_id_envvar: 'GAME_TEAM_SECRETS_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/bf21/KINGSTON_DEMO_BK_OL_SERVER.client.int.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_DEMO_BK_OL_SERVER.client.int.eadp.ea.com.key'
        - path: '/bf21/KINGSTON_DEMO_BK_OL_SERVER.client.int.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_DEMO_BK_OL_SERVER.client.int.eadp.ea.com.crt'
        - path: '/bf21/KINGSTON_DEMO_BK_OL_SERVER.client.prod.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_DEMO_BK_OL_SERVER.client.prod.eadp.ea.com.key'
        - path: '/bf21/KINGSTON_DEMO_BK_OL_SERVER.client.prod.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_DEMO_BK_OL_SERVER.client.prod.eadp.ea.com.crt'
    # Get Kingston Origin & EasyAntiCheat (EAC) key for bundling into Frosty PC builds
    - where:
        build_type: frosty
        platform: win64
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/cobra/automation/projects/kingston/certificates'
          key: 'kingston-lic-private.key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Win32\Origin\GAME_OOA\license.key'
        - path: '/cobra/automation/projects/kingston/certificates'
          key: 'lic-private-kingston-tech-test.key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Win32\Origin\GAME_OOA\license_alpha.key'
        - path: '/cobra/automation/projects/kingston/certificates'
          key: 'kingston-lic-private-trial.key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Win32\Origin\GAME_OOA\trial_license.key'
        - path: '/cobra/automation/projects/kingston/certificates'
          key: 'kingston-game-private.key'
          to: '{TNT_ROOT}\Code\Extension\EasyAntiCheat\HashTool\game_private.key'
        - path: '/cobra/automation/projects/kingston/certificates'
          key: 'kingston-game-public-out-binary.cer'
          to: '{TNT_ROOT}\Code\Extension\EasyAntiCheat\HashTool\game_public.cer'

beta:
  game_binaries: [ 'bf.exe', 'bfTrial.exe' ]
  denuvo:
    artifactory_path: "https://artifacts.at.ea.com/artifactory/dreeu-generic-local/denuvo/kingston/denuvo_files.zip"
    project:
      retail: 'Battlefield Kingston - Beta'
      trial: 'Battlefield Kingston - Trial'
    servers: ''
    server_url: 'https://oetk2-prod.codefusion.technology'
  secrets:
    # Get Kingston server connection keys for bundling into Frosty server builds
    - where:
        build_type: frosty
        platform: server
      url: 'https://ess.ea.com'
      namespace: 'dice-online-casablanca-prod'
      role_id: '0feab1e9-ba7e-acaa-610e-4365569bceb6'
      secret_id_envvar: 'GAME_TEAM_SECRETS_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/bf21/KINGSTON_DEMO_BK_OL_SERVER.client.int.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_DEMO_BK_OL_SERVER.client.int.eadp.ea.com.key'
        - path: '/bf21/KINGSTON_DEMO_BK_OL_SERVER.client.int.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_DEMO_BK_OL_SERVER.client.int.eadp.ea.com.crt'
        - path: '/bf21/KINGSTON_DEMO_BK_OL_SERVER.client.prod.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_DEMO_BK_OL_SERVER.client.prod.eadp.ea.com.key'
        - path: '/bf21/KINGSTON_DEMO_BK_OL_SERVER.client.prod.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_DEMO_BK_OL_SERVER.client.prod.eadp.ea.com.crt'
    # Get Kingston server connection keys for bundling into Frosty linuxserver builds
    - where:
        build_type: frosty
        platform: linuxserver
      url: 'https://ess.ea.com'
      namespace: 'dice-online-casablanca-prod'
      role_id: '0feab1e9-ba7e-acaa-610e-4365569bceb6'
      secret_id_envvar: 'GAME_TEAM_SECRETS_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/bf21/KINGSTON_DEMO_BK_OL_SERVER.client.int.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_DEMO_BK_OL_SERVER.client.int.eadp.ea.com.key'
        - path: '/bf21/KINGSTON_DEMO_BK_OL_SERVER.client.int.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_DEMO_BK_OL_SERVER.client.int.eadp.ea.com.crt'
        - path: '/bf21/KINGSTON_DEMO_BK_OL_SERVER.client.prod.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_DEMO_BK_OL_SERVER.client.prod.eadp.ea.com.key'
        - path: '/bf21/KINGSTON_DEMO_BK_OL_SERVER.client.prod.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_DEMO_BK_OL_SERVER.client.prod.eadp.ea.com.crt'
    # Get Kingston Origin & EasyAntiCheat (EAC) key for bundling into Frosty PC builds
    - where:
        build_type: frosty
        platform: win64
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/cobra/automation/projects/kingston/certificates'
          key: 'kingston-lic-private.key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Win32\Origin\GAME_OOA\license.key'
        - path: '/cobra/automation/projects/kingston/certificates'
          key: 'lic-private-kingston-beta.key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Win32\Origin\GAME_OOA\license_beta.key'
        - path: '/cobra/automation/projects/kingston/certificates'
          key: 'kingston-lic-private-trial.key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Win32\Origin\GAME_OOA\trial_license.key'
        - path: '/cobra/automation/projects/kingston/certificates'
          key: 'kingston-game-private.key'
          to: '{TNT_ROOT}\Code\Extension\EasyAntiCheat\HashTool\game_private.key'
        - path: '/cobra/automation/projects/kingston/certificates'
          key: 'kingston-game-public-out-binary.cer'
          to: '{TNT_ROOT}\Code\Extension\EasyAntiCheat\HashTool\game_public.cer'

criterion:
  build_share: "\\\\eauk-file.eu.ad.ea.com\\Kingston\\Autobuilds"
  bilbo_url: "https://bilbo-kin-uk.dre.dice.se"

  retention_categories:
    code:
      - 'default': 10
      - 'kin-dev': 10
    tnt_local:
      - 'default': 0
    frosty\BattlefieldGame:
      - 'default': 5
      - 'kin-dev': 5

  path_retention:
    - \\eauk-file.eu.ad.ea.com\Kingston\Autobuilds\offsite_basic\kin-dev: 40
    - \\eauk-file.eu.ad.ea.com\Kingston\Autobuilds\offsite_basic_drone\kin-dev: 40

earo:
  build_share: "\\\\ro-nas-dice.eamobile.ad.ea.com\\sync"
  bilbo_url: "https://dice-kin-bilbo-eck.cobra.dre.ea.com"
  studio_location: "EARO"
  metadata_manager:
    primary:
      name: "bilbo_v2"
      index: "earo_bilbo"
      url: "https://dice-kin-bilbo-eck.cobra.dre.ea.com"
      attributes_filename: "earo_build.json"

  retention_categories:
    code:
      - 'default': 20
      - 'kin-dev': 3
      - 'kin-dev-unverified': 3

dicela:
  build_share: "\\\\dice-la.la.ad.ea.com\\Builds\\Kingston"
  bilbo_url: "http://eala-kin-bilbo.la.ad.ea.com:9200/"
  use_onefs_api: "false"
  metadata_manager:
    primary:
      name: "bilbo_v2"
      attributes_filename: "build.json"
    secondary:
      - name: "bilbo"
        index: "bilbo_v1"
        url: "http://eala-kin-bilbo.la.ad.ea.com:9200/"
        attributes_filename: "bilbo_v1.json"

  retention_categories:
    drone:
      - 'default': 10
      - 'kin-dev': 50
      - 'kin-dev-unverified': 100
      - 'kin-stage': 20
      - 'dice-next': 20

  path_retention:
    - not\a\real\path: 9001

insider:
  game_binaries: [ 'bf.exe']
  denuvo:
    artifactory_path: "https://artifacts.at.ea.com/artifactory/dreeu-generic-local/denuvo/kingston/denuvo_files.zip"
    project:
      retail: 'Battlefield Kingston - Insider'
      trial: 'Battlefield Kingston - Trial'
    servers: ''
    server_url: 'https://oetk2-prod.codefusion.technology'
  secrets:
    # Get Kingston server connection keys for bundling into Frosty server builds
    - where:
        build_type: frosty
        platform: server
      url: 'https://ess.ea.com'
      namespace: 'dice-online-casablanca-prod'
      role_id: '0feab1e9-ba7e-acaa-610e-4365569bceb6'
      secret_id_envvar: 'GAME_TEAM_SECRETS_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/bf21/KINGSTON_QVT_BK_OL_SERVER.client.int.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.int.eadp.ea.com.key'
        - path: '/bf21/KINGSTON_QVT_BK_OL_SERVER.client.int.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.int.eadp.ea.com.crt'
        - path: '/bf21/KINGSTON_QVT_BK_OL_SERVER.client.prod.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.prod.eadp.ea.com.key'
        - path: '/bf21/KINGSTON_QVT_BK_OL_SERVER.client.prod.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.prod.eadp.ea.com.crt'
    # Get Kingston server connection keys for bundling into Frosty linuxserver builds
    - where:
        build_type: frosty
        platform: linuxserver
      url: 'https://ess.ea.com'
      namespace: 'dice-online-casablanca-prod'
      role_id: '0feab1e9-ba7e-acaa-610e-4365569bceb6'
      secret_id_envvar: 'GAME_TEAM_SECRETS_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/bf21/KINGSTON_QVT_BK_OL_SERVER.client.int.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.int.eadp.ea.com.key'
        - path: '/bf21/KINGSTON_QVT_BK_OL_SERVER.client.int.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.int.eadp.ea.com.crt'
        - path: '/bf21/KINGSTON_QVT_BK_OL_SERVER.client.prod.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.prod.eadp.ea.com.key'
        - path: '/bf21/KINGSTON_QVT_BK_OL_SERVER.client.prod.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.prod.eadp.ea.com.crt'
    # Get Kingston Origin & EasyAntiCheat (EAC) key for bundling into Frosty PC builds
    - where:
        build_type: frosty
        platform: win64
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/cobra/automation/projects/kingston/certificates'
          key: 'kingston-lic-private.key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Win32\Origin\GAME_OOA\license.key'
        - path: '/cobra/automation/projects/kingston/certificates'
          key: 'lic-private-insider.key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Win32\Origin\GAME_OOA\license_insider.key'
        - path: '/cobra/automation/projects/kingston/certificates'
          key: 'kingston-lic-private-trial.key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Win32\Origin\GAME_OOA\trial_license.key'
        - path: '/cobra/automation/projects/kingston/certificates'
          key: 'kingston-game-private.key'
          to: '{TNT_ROOT}\Code\Extension\EasyAntiCheat\HashTool\game_private.key'
        - path: '/cobra/automation/projects/kingston/certificates'
          key: 'kingston-game-public-out-binary.cer'
          to: '{TNT_ROOT}\Code\Extension\EasyAntiCheat\HashTool\game_public.cer'
    - where:
        project_secrets: true
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/kingston/secrets'
