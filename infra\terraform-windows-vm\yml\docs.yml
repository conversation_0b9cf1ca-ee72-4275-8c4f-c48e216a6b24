#**********************************************#
#              TERRAFORM DOCS PIPE              #
#**********************************************#

pages:
  stage: generate-docs
  image: $dockerterraform
  dependencies: []
  tags:
    - glaas-shared-k8s
  script:
    - for d in modules/*/; do terraform-docs markdown table $d --output-file doc.md; done && for d in projects/*/; do terraform-docs markdown table $d --output-file doc.md; done
    - cp --parents modules/**/doc.md docs/ && cp --parents projects/**/doc.md docs/
    - cp README.md docs/ && cp CONTRIBUTING.md docs/
    - mkdocs build --strict --verbose
  artifacts:
    paths:
      - public
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_BRANCH == 'master'
