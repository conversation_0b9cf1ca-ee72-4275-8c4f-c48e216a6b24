#*************************************************************
#  Sets up the initial needs to provide a vsphere instance
#*************************************************************
# Point to our provider
# https://www.terraform.io/docs/providers/vsphere/index.html
# *************************************************************
terraform {
  required_version = ">= 1.0.0"
  required_providers {
    vsphere = {
      version = "= 2.12.0" #locking Version to 2.12.0 to avoid introducing breaks
    }
    random = {
      source  = "hashicorp/random"
      version = "= 3.0.0"
    }
  }
}
provider "vsphere" {
  user           = var.vsphere_user
  password       = var.vsphere_password
  vsphere_server = "eala-vcenter.la.ad.ea.com"
  # if you have a self-signed cert or not
  allow_unverified_ssl = true
  persist_session      = true
}

# *************************************************************
# Default Settings to use
# https://www.terraform.io/docs/configuration/variables.html
# *************************************************************
variable "packer_template" {
  type        = string
  default     = "win10_22H2-cobra-v1.1101.0b76e848"
  description = "Template to use for VMs in this project"
}
variable "network" {
  type        = string
  default     = "VLAN32 ***********/22"
  description = "Network to use for VMs in this project"
}
variable "datacenter" {
  type        = string
  default     = "Los Angeles"
  description = "Datacenter to use for VMs in this project"
}
variable "project_name" {
  default     = "fb1_dev_na_eala"
  description = "Name of the project e.g. diceupgradenext, walrus ect"
}
variable "disk_size" {
  default     = "700"
  description = "Size of the disk required in GB e.g. 250 = 250GB for C:"
}
variable "hardware_version" {
  type        = number
  default     = 15
  description = "The hardware version for VMs in this project"
}
variable "local_admin_group" {
  type        = string
  default     = "AD\\BCT.SECURITY.BUILD.AGENTS"
  description = "Local admin group used for ansible and jenkins authentication"
}
variable "domain_ou" {
  type        = string
  default     = "OU=Granite,OU=EALA-Build Servers,DC=la,DC=ad,DC=ea,DC=com"
  description = "The OU to use for the domain join"
}
variable "domain_name" {
  type        = string
  default     = "dice.ad.ea.com"
  description = "The domain to use for the domain join"
}
variable "vsphere_location" {
  type        = string
  default     = "DICE/terraform-nodes/bct_ch1_eala"
  description = "The folder to use for the VMs in vSphere"
}

# *************************************************************
# Environmental Settings to use
# https://www.terraform.io/docs/configuration/variables.html
# *************************************************************
variable "vsphere_server" {}

variable "vsphere_user" {}

variable "vsphere_password" {}

variable "local_username" {}

variable "local_password" {}

variable "domain_admin" {}

variable "domain_password" {}

variable "project_dir" {}

variable "ARTIFACTORY_USER" {}

variable "ARTIFACTORY_PASSWORD" {}

variable "ARTIFACTORY_URL" {}

variable "commit_sha" {}

variable "commit_user" {}

variable "commit_url" {}
