[{"ARN": "arn:aws:ssm:ap-northeast-3::parameter/aws/service/ami-amazon-linux-latest/al2023-ami-kernel-6.1-arm64", "DataType": "text", "LastModifiedDate": 1679607570.965, "Name": "/aws/service/ami-amazon-linux-latest/al2023-ami-kernel-6.1-arm64", "Type": "String", "Value": "ami-02c5910a9518a0eff", "Version": 4}, {"ARN": "arn:aws:ssm:ap-northeast-3::parameter/aws/service/ami-amazon-linux-latest/al2023-ami-kernel-6.1-x86_64", "DataType": "text", "LastModifiedDate": 1679607571.115, "Name": "/aws/service/ami-amazon-linux-latest/al2023-ami-kernel-6.1-x86_64", "Type": "String", "Value": "ami-0265608a60d05ecf8", "Version": 4}, {"ARN": "arn:aws:ssm:ap-northeast-3::parameter/aws/service/ami-amazon-linux-latest/al2023-ami-minimal-kernel-6.1-arm64", "DataType": "text", "LastModifiedDate": 1679607571.26, "Name": "/aws/service/ami-amazon-linux-latest/al2023-ami-minimal-kernel-6.1-arm64", "Type": "String", "Value": "ami-07c2fadda630e1f2b", "Version": 4}, {"ARN": "arn:aws:ssm:ap-northeast-3::parameter/aws/service/ami-amazon-linux-latest/al2023-ami-minimal-kernel-6.1-x86_64", "DataType": "text", "LastModifiedDate": 1679607571.409, "Name": "/aws/service/ami-amazon-linux-latest/al2023-ami-minimal-kernel-6.1-x86_64", "Type": "String", "Value": "ami-04172c1fb26f001c2", "Version": 4}, {"ARN": "arn:aws:ssm:ap-northeast-3::parameter/aws/service/ami-amazon-linux-latest/al2023-ami-minimal-kernel-default-arm64", "DataType": "text", "LastModifiedDate": 1679607571.835, "Name": "/aws/service/ami-amazon-linux-latest/al2023-ami-minimal-kernel-default-arm64", "Type": "String", "Value": "ami-07c2fadda630e1f2b", "Version": 4}, {"ARN": "arn:aws:ssm:ap-northeast-3::parameter/aws/service/ami-amazon-linux-latest/amzn-ami-hvm-x86_64-gp2", "DataType": "text", "LastModifiedDate": 1679610957.826, "Name": "/aws/service/ami-amazon-linux-latest/amzn-ami-hvm-x86_64-gp2", "Type": "String", "Value": "ami-047987250a07b6edb", "Version": 34}, {"ARN": "arn:aws:ssm:ap-northeast-3::parameter/aws/service/ami-amazon-linux-latest/amzn-ami-hvm-x86_64-s3", "DataType": "text", "LastModifiedDate": 1679610957.959, "Name": "/aws/service/ami-amazon-linux-latest/amzn-ami-hvm-x86_64-s3", "Type": "String", "Value": "ami-04383391a340a4e84", "Version": 34}, {"ARN": "arn:aws:ssm:ap-northeast-3::parameter/aws/service/ami-amazon-linux-latest/amzn2-ami-hvm-x86_64-ebs", "DataType": "text", "LastModifiedDate": 1679601496.425, "Name": "/aws/service/ami-amazon-linux-latest/amzn2-ami-hvm-x86_64-ebs", "Type": "String", "Value": "ami-0e5385dfa953b17ce", "Version": 48}, {"ARN": "arn:aws:ssm:ap-northeast-3::parameter/aws/service/ami-amazon-linux-latest/amzn2-ami-hvm-x86_64-gp2", "DataType": "text", "LastModifiedDate": 1679601496.571, "Name": "/aws/service/ami-amazon-linux-latest/amzn2-ami-hvm-x86_64-gp2", "Type": "String", "Value": "ami-08da182314b5f34cd", "Version": 48}, {"ARN": "arn:aws:ssm:ap-northeast-3::parameter/aws/service/ami-amazon-linux-latest/amzn2-ami-kernel-5.10-hvm-x86_64-ebs", "DataType": "text", "LastModifiedDate": 1679601496.865, "Name": "/aws/service/ami-amazon-linux-latest/amzn2-ami-kernel-5.10-hvm-x86_64-ebs", "Type": "String", "Value": "ami-098ccd5903dbfd1a2", "Version": 34}, {"ARN": "arn:aws:ssm:ap-northeast-3::parameter/aws/service/ami-amazon-linux-latest/al2022-ami-kernel-5.15-x86_64", "DataType": "text", "LastModifiedDate": 1675120201.658, "Name": "/aws/service/ami-amazon-linux-latest/al2022-ami-kernel-5.15-x86_64", "Type": "String", "Value": "ami-016697c7c6ea2dd28", "Version": 21}, {"ARN": "arn:aws:ssm:ap-northeast-3::parameter/aws/service/ami-amazon-linux-latest/al2022-ami-kernel-default-x86_64", "DataType": "text", "LastModifiedDate": 1675120202.305, "Name": "/aws/service/ami-amazon-linux-latest/al2022-ami-kernel-default-x86_64", "Type": "String", "Value": "ami-016697c7c6ea2dd28", "Version": 20}, {"ARN": "arn:aws:ssm:ap-northeast-3::parameter/aws/service/ami-amazon-linux-latest/al2022-ami-minimal-kernel-5.15-arm64", "DataType": "text", "LastModifiedDate": 1675120201.819, "Name": "/aws/service/ami-amazon-linux-latest/al2022-ami-minimal-kernel-5.15-arm64", "Type": "String", "Value": "ami-0b495b918eb9cb788", "Version": 17}, {"ARN": "arn:aws:ssm:ap-northeast-3::parameter/aws/service/ami-amazon-linux-latest/al2022-ami-minimal-kernel-5.15-x86_64", "DataType": "text", "LastModifiedDate": 1675120201.978, "Name": "/aws/service/ami-amazon-linux-latest/al2022-ami-minimal-kernel-5.15-x86_64", "Type": "String", "Value": "ami-0d2ab4f2218ecc3b4", "Version": 21}, {"ARN": "arn:aws:ssm:ap-northeast-3::parameter/aws/service/ami-amazon-linux-latest/al2023-ami-kernel-default-x86_64", "DataType": "text", "LastModifiedDate": 1679607571.685, "Name": "/aws/service/ami-amazon-linux-latest/al2023-ami-kernel-default-x86_64", "Type": "String", "Value": "ami-0265608a60d05ecf8", "Version": 4}, {"ARN": "arn:aws:ssm:ap-northeast-3::parameter/aws/service/ami-amazon-linux-latest/al2023-ami-minimal-kernel-default-x86_64", "DataType": "text", "LastModifiedDate": 1679607571.974, "Name": "/aws/service/ami-amazon-linux-latest/al2023-ami-minimal-kernel-default-x86_64", "Type": "String", "Value": "ami-04172c1fb26f001c2", "Version": 4}, {"ARN": "arn:aws:ssm:ap-northeast-3::parameter/aws/service/ami-amazon-linux-latest/amzn-ami-hvm-x86_64-ebs", "DataType": "text", "LastModifiedDate": 1679610957.67, "Name": "/aws/service/ami-amazon-linux-latest/amzn-ami-hvm-x86_64-ebs", "Type": "String", "Value": "ami-04fcabf61c896ec9e", "Version": 34}, {"ARN": "arn:aws:ssm:ap-northeast-3::parameter/aws/service/ami-amazon-linux-latest/amzn-ami-minimal-hvm-x86_64-s3", "DataType": "text", "LastModifiedDate": 1679610958.258, "Name": "/aws/service/ami-amazon-linux-latest/amzn-ami-minimal-hvm-x86_64-s3", "Type": "String", "Value": "ami-0c97ae69a1d11c719", "Version": 34}, {"ARN": "arn:aws:ssm:ap-northeast-3::parameter/aws/service/ami-amazon-linux-latest/amzn2-ami-hvm-arm64-gp2", "DataType": "text", "LastModifiedDate": 1679601496.284, "Name": "/aws/service/ami-amazon-linux-latest/amzn2-ami-hvm-arm64-gp2", "Type": "String", "Value": "ami-0a7be84ee19104f59", "Version": 48}, {"ARN": "arn:aws:ssm:ap-northeast-3::parameter/aws/service/ami-amazon-linux-latest/amzn2-ami-minimal-hvm-arm64-ebs", "DataType": "text", "LastModifiedDate": 1679601497.145, "Name": "/aws/service/ami-amazon-linux-latest/amzn2-ami-minimal-hvm-arm64-ebs", "Type": "String", "Value": "ami-090f10b738ab0921c", "Version": 48}, {"ARN": "arn:aws:ssm:ap-northeast-3::parameter/aws/service/ami-amazon-linux-latest/al2022-ami-kernel-5.15-arm64", "DataType": "text", "LastModifiedDate": 1675120201.491, "Name": "/aws/service/ami-amazon-linux-latest/al2022-ami-kernel-5.15-arm64", "Type": "String", "Value": "ami-05f60a0de019c1f90", "Version": 17}, {"ARN": "arn:aws:ssm:ap-northeast-3::parameter/aws/service/ami-amazon-linux-latest/al2022-ami-kernel-default-arm64", "DataType": "text", "LastModifiedDate": 1675120202.141, "Name": "/aws/service/ami-amazon-linux-latest/al2022-ami-kernel-default-arm64", "Type": "String", "Value": "ami-05f60a0de019c1f90", "Version": 17}, {"ARN": "arn:aws:ssm:ap-northeast-3::parameter/aws/service/ami-amazon-linux-latest/al2022-ami-minimal-kernel-default-arm64", "DataType": "text", "LastModifiedDate": 1675120202.47, "Name": "/aws/service/ami-amazon-linux-latest/al2022-ami-minimal-kernel-default-arm64", "Type": "String", "Value": "ami-0b495b918eb9cb788", "Version": 17}, {"ARN": "arn:aws:ssm:ap-northeast-3::parameter/aws/service/ami-amazon-linux-latest/al2022-ami-minimal-kernel-default-x86_64", "DataType": "text", "LastModifiedDate": 1675120202.627, "Name": "/aws/service/ami-amazon-linux-latest/al2022-ami-minimal-kernel-default-x86_64", "Type": "String", "Value": "ami-0d2ab4f2218ecc3b4", "Version": 20}, {"ARN": "arn:aws:ssm:ap-northeast-3::parameter/aws/service/ami-amazon-linux-latest/al2023-ami-kernel-default-arm64", "DataType": "text", "LastModifiedDate": 1679607571.555, "Name": "/aws/service/ami-amazon-linux-latest/al2023-ami-kernel-default-arm64", "Type": "String", "Value": "ami-02c5910a9518a0eff", "Version": 4}, {"ARN": "arn:aws:ssm:ap-northeast-3::parameter/aws/service/ami-amazon-linux-latest/amzn-ami-minimal-hvm-x86_64-ebs", "DataType": "text", "LastModifiedDate": 1679610958.107, "Name": "/aws/service/ami-amazon-linux-latest/amzn-ami-minimal-hvm-x86_64-ebs", "Type": "String", "Value": "ami-0939c4079172fa0e5", "Version": 34}, {"ARN": "arn:aws:ssm:ap-northeast-3::parameter/aws/service/ami-amazon-linux-latest/amzn2-ami-kernel-5.10-hvm-arm64-gp2", "DataType": "text", "LastModifiedDate": 1679601496.727, "Name": "/aws/service/ami-amazon-linux-latest/amzn2-ami-kernel-5.10-hvm-arm64-gp2", "Type": "String", "Value": "ami-0f71a22b3a8ab5dbd", "Version": 34}, {"ARN": "arn:aws:ssm:ap-northeast-3::parameter/aws/service/ami-amazon-linux-latest/amzn2-ami-kernel-5.10-hvm-x86_64-gp2", "DataType": "text", "LastModifiedDate": 1679601497.014, "Name": "/aws/service/ami-amazon-linux-latest/amzn2-ami-kernel-5.10-hvm-x86_64-gp2", "Type": "String", "Value": "ami-012c6a03c2e59a445", "Version": 34}, {"ARN": "arn:aws:ssm:ap-northeast-3::parameter/aws/service/ami-amazon-linux-latest/amzn2-ami-minimal-hvm-x86_64-ebs", "DataType": "text", "LastModifiedDate": 1679601497.287, "Name": "/aws/service/ami-amazon-linux-latest/amzn2-ami-minimal-hvm-x86_64-ebs", "Type": "String", "Value": "ami-06e23a94199541842", "Version": 48}]