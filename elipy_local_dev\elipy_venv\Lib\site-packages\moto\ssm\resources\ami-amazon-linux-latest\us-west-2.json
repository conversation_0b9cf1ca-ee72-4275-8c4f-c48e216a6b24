[{"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/al2023-ami-kernel-6.1-arm64", "DataType": "text", "LastModifiedDate": 1679607600.091, "Name": "/aws/service/ami-amazon-linux-latest/al2023-ami-kernel-6.1-arm64", "Type": "String", "Value": "ami-0699f753302dd8b00", "Version": 4}, {"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/al2023-ami-kernel-6.1-x86_64", "DataType": "text", "LastModifiedDate": 1679607600.145, "Name": "/aws/service/ami-amazon-linux-latest/al2023-ami-kernel-6.1-x86_64", "Type": "String", "Value": "ami-0efa651876de2a5ce", "Version": 4}, {"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/al2023-ami-minimal-kernel-6.1-arm64", "DataType": "text", "LastModifiedDate": 1679607600.188, "Name": "/aws/service/ami-amazon-linux-latest/al2023-ami-minimal-kernel-6.1-arm64", "Type": "String", "Value": "ami-077031e562122f4a0", "Version": 4}, {"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/al2023-ami-minimal-kernel-6.1-x86_64", "DataType": "text", "LastModifiedDate": 1679607600.24, "Name": "/aws/service/ami-amazon-linux-latest/al2023-ami-minimal-kernel-6.1-x86_64", "Type": "String", "Value": "ami-0f182f5235b83534f", "Version": 4}, {"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/al2023-ami-minimal-kernel-default-arm64", "DataType": "text", "LastModifiedDate": 1679607600.361, "Name": "/aws/service/ami-amazon-linux-latest/al2023-ami-minimal-kernel-default-arm64", "Type": "String", "Value": "ami-077031e562122f4a0", "Version": 4}, {"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/amzn-ami-hvm-x86_64-gp2", "DataType": "text", "LastModifiedDate": 1679610987.409, "Name": "/aws/service/ami-amazon-linux-latest/amzn-ami-hvm-x86_64-gp2", "Type": "String", "Value": "ami-026b583c4cf6d302a", "Version": 55}, {"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/amzn-ami-hvm-x86_64-s3", "DataType": "text", "LastModifiedDate": 1679610987.466, "Name": "/aws/service/ami-amazon-linux-latest/amzn-ami-hvm-x86_64-s3", "Type": "String", "Value": "ami-0b58573daf5a536a6", "Version": 55}, {"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/amzn2-ami-hvm-x86_64-ebs", "DataType": "text", "LastModifiedDate": 1679601525.2, "Name": "/aws/service/ami-amazon-linux-latest/amzn2-ami-hvm-x86_64-ebs", "Type": "String", "Value": "ami-04d0fca9fc2734804", "Version": 81}, {"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/amzn2-ami-hvm-x86_64-gp2", "DataType": "text", "LastModifiedDate": 1679601525.249, "Name": "/aws/service/ami-amazon-linux-latest/amzn2-ami-hvm-x86_64-gp2", "Type": "String", "Value": "ami-001e91409ff66b7b0", "Version": 81}, {"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/amzn2-ami-kernel-5.10-hvm-x86_64-ebs", "DataType": "text", "LastModifiedDate": 1679601525.35, "Name": "/aws/service/ami-amazon-linux-latest/amzn2-ami-kernel-5.10-hvm-x86_64-ebs", "Type": "String", "Value": "ami-014b73ac65a3b2517", "Version": 34}, {"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/al2022-ami-kernel-5.15-x86_64", "DataType": "text", "LastModifiedDate": 1675120232.055, "Name": "/aws/service/ami-amazon-linux-latest/al2022-ami-kernel-5.15-x86_64", "Type": "String", "Value": "ami-0d80f4c31a7caf289", "Version": 21}, {"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/al2022-ami-kernel-default-x86_64", "DataType": "text", "LastModifiedDate": 1675120232.263, "Name": "/aws/service/ami-amazon-linux-latest/al2022-ami-kernel-default-x86_64", "Type": "String", "Value": "ami-0d80f4c31a7caf289", "Version": 20}, {"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/al2022-ami-minimal-kernel-5.15-arm64", "DataType": "text", "LastModifiedDate": 1675120232.113, "Name": "/aws/service/ami-amazon-linux-latest/al2022-ami-minimal-kernel-5.15-arm64", "Type": "String", "Value": "ami-0cff6d9b6e370aefa", "Version": 17}, {"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/al2022-ami-minimal-kernel-5.15-x86_64", "DataType": "text", "LastModifiedDate": 1675120232.154, "Name": "/aws/service/ami-amazon-linux-latest/al2022-ami-minimal-kernel-5.15-x86_64", "Type": "String", "Value": "ami-03751074b1c333ad5", "Version": 21}, {"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/al2023-ami-kernel-default-x86_64", "DataType": "text", "LastModifiedDate": 1679607600.328, "Name": "/aws/service/ami-amazon-linux-latest/al2023-ami-kernel-default-x86_64", "Type": "String", "Value": "ami-0efa651876de2a5ce", "Version": 4}, {"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/al2023-ami-minimal-kernel-default-x86_64", "DataType": "text", "LastModifiedDate": 1679607600.412, "Name": "/aws/service/ami-amazon-linux-latest/al2023-ami-minimal-kernel-default-x86_64", "Type": "String", "Value": "ami-0f182f5235b83534f", "Version": 4}, {"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/amzn-ami-hvm-x86_64-ebs", "DataType": "text", "LastModifiedDate": 1679610987.354, "Name": "/aws/service/ami-amazon-linux-latest/amzn-ami-hvm-x86_64-ebs", "Type": "String", "Value": "ami-023863d0733045a6c", "Version": 55}, {"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/amzn-ami-minimal-hvm-x86_64-s3", "DataType": "text", "LastModifiedDate": 1679610987.598, "Name": "/aws/service/ami-amazon-linux-latest/amzn-ami-minimal-hvm-x86_64-s3", "Type": "String", "Value": "ami-09fd0ebb91f7e665e", "Version": 55}, {"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/amzn2-ami-hvm-arm64-gp2", "DataType": "text", "LastModifiedDate": 1679601525.148, "Name": "/aws/service/ami-amazon-linux-latest/amzn2-ami-hvm-arm64-gp2", "Type": "String", "Value": "ami-04d0a8d2afaa4231b", "Version": 69}, {"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/amzn2-ami-minimal-hvm-arm64-ebs", "DataType": "text", "LastModifiedDate": 1679601525.448, "Name": "/aws/service/ami-amazon-linux-latest/amzn2-ami-minimal-hvm-arm64-ebs", "Type": "String", "Value": "ami-0bc48d58a3f6d97d8", "Version": 69}, {"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/al2022-ami-kernel-5.15-arm64", "DataType": "text", "LastModifiedDate": 1675120232.002, "Name": "/aws/service/ami-amazon-linux-latest/al2022-ami-kernel-5.15-arm64", "Type": "String", "Value": "ami-03b2f507467c32460", "Version": 17}, {"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/al2022-ami-minimal-kernel-default-arm64", "DataType": "text", "LastModifiedDate": 1675120232.304, "Name": "/aws/service/ami-amazon-linux-latest/al2022-ami-minimal-kernel-default-arm64", "Type": "String", "Value": "ami-0cff6d9b6e370aefa", "Version": 17}, {"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/al2022-ami-minimal-kernel-default-x86_64", "DataType": "text", "LastModifiedDate": 1675120232.338, "Name": "/aws/service/ami-amazon-linux-latest/al2022-ami-minimal-kernel-default-x86_64", "Type": "String", "Value": "ami-03751074b1c333ad5", "Version": 20}, {"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/amzn-ami-minimal-hvm-x86_64-ebs", "DataType": "text", "LastModifiedDate": 1679610987.551, "Name": "/aws/service/ami-amazon-linux-latest/amzn-ami-minimal-hvm-x86_64-ebs", "Type": "String", "Value": "ami-04b517e4c8088c331", "Version": 55}, {"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/amzn-ami-minimal-pv-x86_64-ebs", "DataType": "text", "LastModifiedDate": 1679610987.651, "Name": "/aws/service/ami-amazon-linux-latest/amzn-ami-minimal-pv-x86_64-ebs", "Type": "String", "Value": "ami-0bfcf28b41b76f07f", "Version": 55}, {"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/amzn-ami-minimal-pv-x86_64-s3", "DataType": "text", "LastModifiedDate": 1679610987.696, "Name": "/aws/service/ami-amazon-linux-latest/amzn-ami-minimal-pv-x86_64-s3", "Type": "String", "Value": "ami-00801e209e7e6f23a", "Version": 55}, {"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/amzn-ami-pv-x86_64-ebs", "DataType": "text", "LastModifiedDate": 1679610987.75, "Name": "/aws/service/ami-amazon-linux-latest/amzn-ami-pv-x86_64-ebs", "Type": "String", "Value": "ami-0a574d017e3f30ade", "Version": 55}, {"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/amzn-ami-pv-x86_64-s3", "DataType": "text", "LastModifiedDate": 1679610987.788, "Name": "/aws/service/ami-amazon-linux-latest/amzn-ami-pv-x86_64-s3", "Type": "String", "Value": "ami-0e60951cbbd7d32ea", "Version": 55}, {"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/amzn2-ami-kernel-5.10-hvm-arm64-gp2", "DataType": "text", "LastModifiedDate": 1679601525.297, "Name": "/aws/service/ami-amazon-linux-latest/amzn2-ami-kernel-5.10-hvm-arm64-gp2", "Type": "String", "Value": "ami-04f8e5061ee50b423", "Version": 34}, {"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/amzn2-ami-kernel-5.10-hvm-x86_64-gp2", "DataType": "text", "LastModifiedDate": 1679601525.404, "Name": "/aws/service/ami-amazon-linux-latest/amzn2-ami-kernel-5.10-hvm-x86_64-gp2", "Type": "String", "Value": "ami-07f3ef11ec14a1ea3", "Version": 34}, {"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/al2022-ami-kernel-default-arm64", "DataType": "text", "LastModifiedDate": 1675120232.206, "Name": "/aws/service/ami-amazon-linux-latest/al2022-ami-kernel-default-arm64", "Type": "String", "Value": "ami-03b2f507467c32460", "Version": 17}, {"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/al2023-ami-kernel-default-arm64", "DataType": "text", "LastModifiedDate": 1679607600.282, "Name": "/aws/service/ami-amazon-linux-latest/al2023-ami-kernel-default-arm64", "Type": "String", "Value": "ami-0699f753302dd8b00", "Version": 4}, {"ARN": "arn:aws:ssm:us-west-2::parameter/aws/service/ami-amazon-linux-latest/amzn2-ami-minimal-hvm-x86_64-ebs", "DataType": "text", "LastModifiedDate": 1679601525.481, "Name": "/aws/service/ami-amazon-linux-latest/amzn2-ami-minimal-hvm-x86_64-ebs", "Type": "String", "Value": "ami-099e00fe4091e48af", "Version": 81}]